namespace Reception;

/// <summary>
///     弘讯注塑机
/// </summary>
public class HongXunAkDoubleData
{
    #region 温度设定F8F2

    /// <summary>
    ///     A枪温1~温7设定值
    /// </summary>
    public short[] A_TempSet { get; set; } = new short[7];

    /// <summary>
    ///     B枪温1~温7设定值
    /// </summary>
    public short[] B_TempSet { get; set; } = new short[7];
    
    #endregion 温度设定F8F2

    #region 处理43_44的数据，来源HMI

    /// <summary>
    ///     A枪温度一实际~温度七实际
    /// </summary>
    public short[] A_TempAct { get; set; } = new short[7];

    /// <summary>
    ///     B枪温度一实际~温度七实际
    /// </summary>
    public short[] B_TempAct { get; set; } = new short[7];
    
    #endregion 处理43_44的数据，来源HMI

    #region F2F2MoldSetting（开关模设定F2F2）

    /// <summary>
    ///     关模 1~5段压力
    /// </summary>
    public short[] MoldClosingPressure { get; set; } = new short[5];

    /// <summary>
    ///     关模 1~5段速度
    /// </summary>
    public short[] MoldClosingSpeed { get; set; } = new short[5];

    /// <summary>
    ///     关模 1~5段位置
    /// </summary>
    public double[] MoldClosingPosition { get; set; } = new double[5];

    /// <summary>
    ///     模具冷却时间
    /// </summary>
    public double MoldCoolingTime { get; set; }

    /// <summary>
    ///     再循环计时
    /// </summary>
    public double RecyclingTime { get; set; }

    /// <summary>
    ///     开模压力
    /// </summary>
    public short[] MoldOpeningPressure { get; set; } = new short[5];

    /// <summary>
    ///     开模速度
    /// </summary>
    public short[] MoldOpeningSpeed { get; set; } = new short[5];

    /// <summary>
    ///     开模 1~6段位置
    /// </summary>
    public double[] MoldOpeningPosition { get; set; } = new double[5];

    /// <summary>
    ///     开模行程
    /// </summary>
    public double MoldOpeningStroke { get; set; }

    /// <summary>
    /// 模具冷却计时低位数据
    /// </summary>
    public short MoldCoolingTimeLow { get; set; }

    /// <summary>
    /// 模具冷却计时低位数据 true = 低；false = 高
    /// </summary>
    public bool GetMoldCoolingTimeLow { get; set; }

    /// <summary>
    /// 模具冷却计时高位数据
    /// </summary>
    public short MoldCoolingTimeHigh { get; set; }

    #endregion F2F2MoldSetting（开关模设定F2F2）

    #region 处理45_43的数据，来源HMI

    /// <summary>
    ///     上模循环时间
    /// </summary>
    public double MsCycleTime { get; set; }

    /// <summary>
    ///     上模射出时间
    /// </summary>
    public double MsInjectionTime { get; set; }

    /// <summary>
    ///     上模转保时间
    /// </summary>
    public double MsHoldingTime { get; set; }

    /// <summary>
    ///     上模储料时间
    /// </summary>
    public double MsStorageTime { get; set; }

    /// <summary>
    ///     上模关模计时
    /// </summary>
    public double MsMoldCloseTime { get; set; }

    /// <summary>
    ///     上模低压计时
    /// </summary>
    public double MsLowPressureTime { get; set; }

    /// <summary>
    ///     上模高压计时
    /// </summary>
    public double MsHighPressureTime { get; set; }

    /// <summary>
    ///     上模推力座位置
    /// </summary>
    public double MsEjectorPos { get; set; }

    /// <summary>
    ///     上模开模计时
    /// </summary>
    public double MsMoldOpenTime { get; set; }

    /// <summary>
    ///     上模转保压力
    /// </summary>
    public double MsHoldingPressure { get; set; }

    /// <summary>
    ///     上模射出起点
    /// </summary>
    public double MsInjectionStartPos { get; set; }

    /// <summary>
    ///     上模保压起点
    /// </summary>
    public double MsHoldingStartPos { get; set; }

    /// <summary>
    ///     上模射出终点位置
    /// </summary>
    public double MsInjectionEndPos { get; set; }

    /// <summary>
    ///     上模射出监控位置
    /// </summary>
    public double MsInjectionMonPos { get; set; }

    /// <summary>
    ///     上模射出尖压
    /// </summary>
    public double MsInjectionPeakPressure { get; set; }

    /// <summary>
    ///     上模储料尖压
    /// </summary>
    public double MsStoragePeakPressure { get; set; }

    /// <summary>
    ///     上模最大射速
    /// </summary>
    public double MsInjectionSpeedMax { get; set; }

    /// <summary>
    ///     上模取件时间
    /// </summary>
    public double MsPickUpTime { get; set; }

    #endregion 处理45_43的数据，来源HMI

    #region 处理43_41的数据，来源HMI

    /// <summary>
    ///     模式
    /// </summary>
    public string Mode { get; set; }

    /// <summary>
    ///     报警信息
    /// </summary>
    public string AlarmStr { get; set; }

    /// <summary>
    ///     输出压力
    /// </summary>
    public short OutputPressure { get; set; }

    /// <summary>
    ///     输出速度
    /// </summary>
    public short OutputSpeed { get; set; }

    /// <summary>
    ///     输出背压
    /// </summary>
    public short OutputBackPressure { get; set; }

    /// <summary>
    ///     射出位置实时值
    /// </summary>
    public double InjectionPositionAct { get; set; }

    /// <summary>
    ///     推力座位置
    /// </summary>
    public double MoldPosition { get; set; }

    /// <summary>
    ///     托模位置
    /// </summary>
    public double EjectorPosition { get; set; }

    /// <summary>
    ///     开模总数
    /// </summary>
    public int MoldOpeningTotal { get; set; }

    /// <summary>
    ///     低计数
    /// </summary>
    public int MoldOpeningTotalLow { get; set; }

    /// <summary>
    ///     是否是低
    /// </summary>
    public bool GetMoldOpeningTotalLow { get; set; }

    /// <summary>
    ///     高计数
    /// </summary>
    public int MoldOpeningTotalHigh { get; set; }

    /// <summary>
    ///     设备循环时间
    /// </summary>
    public double CycleTime { get; set; }


    #endregion 处理43_41的数据，来源HMI

    #region F3F2InjectionSetting（射出设定F3F2）

    /// <summary>
    ///     保压 1~6段压力
    /// </summary>
    public short[] HoldingPressure { get; set; } = new short[6];

    /// <summary>
    ///     保压 1~6段速度
    /// </summary>
    public short[] HoldingSpeed { get; set; } = new short[6];

    /// <summary>
    ///     保压 1~6段时间
    /// </summary>
    public double[] HoldingTime { get; set; } = new double[6];

    /// <summary>
    ///     射出 1~6 段压力
    /// </summary>
    public double[] InjectionPressure { get; set; } = new double[6];

    /// <summary>
    ///     射出 1~6 段速度
    /// </summary>
    public double[] InjectionSpeed { get; set; } = new double[6];

    /// <summary>
    ///     射出 1~6段位置
    /// </summary>
    public double[] InjectionPosition { get; set; } = new double[6];

    /// <summary>
    ///     转保压压力
    /// </summary>
    public short SwitchHoldingPressure { get; set; }

    /// <summary>
    ///     转保压位置
    /// </summary>
    public double SwitchHoldingPosition { get; set; }

    /// <summary>
    ///     转保压选择，0表示位置，1表示时间，2表示压力
    /// </summary>
    public short SwitchHoldingSelection { get; set; }

    /// <summary>
    ///     转保压时间设定
    /// </summary>
    public double SwitchHoldingTime { get; set; }

    #endregion F3F2InjectionSetting（射出设定F3F2）

    #region F4F2StorageSetting（储料设定，包括储料和射退）

    /// <summary>
    ///     储料1~5段压力
    /// </summary>
    public short[] StoragePressure { get; set; } = new short[5];

    /// <summary>
    ///     储料1~5段背压
    /// </summary>
    public short[] StorageBackPressure { get; set; } = new short[5];

    /// <summary>
    ///     储料1~5段速度
    /// </summary>
    public short[] StorageSpeed { get; set; } = new short[5];

    /// <summary>
    ///     储料1~5段位置
    /// </summary>
    public double[] StoragePosition { get; set; } = new double[5];

    /// <summary>
    ///     储前冷却
    /// </summary>
    public double CoolingBeforeStorage { get; set; }

    /// <summary>
    ///     射退压力
    /// </summary>
    public short InjectionBackPressure { get; set; }

    /// <summary>
    ///     射退速度
    /// </summary>
    public short InjectionBackSpeed { get; set; }

    /// <summary>
    ///     射退距离
    /// </summary>
    public double InjectionBackDistance { get; set; }

    /// <summary>
    ///     射退模式
    /// </summary>
    public double InjectionBackMode { get; set; }

    /// <summary>
    ///     储前射退距离
    /// </summary>
    public double InjectionBackDistanceBeforeStorage { get; set; }

    #endregion F4F2StorageSetting（储料设定，包括储料和射退）

    #region F5F2EjectorSetting （托模设定F5F2）

    /// <summary>
    ///     托模进 1~4段压力
    /// </summary>
    public short[] EjectorForwardPressure { get; set; } = new short[4];

    /// <summary>
    ///     托模退 1~4段压力
    /// </summary>
    public short[] EjectorBackwardPressure { get; set; } = new short[4];

    /// <summary>
    ///     托模进 1~3速度
    /// </summary>
    public short[] EjectorForwardSpeed { get; set; } = new short[4];

    /// <summary>
    ///     托模退 1~4 段速度
    /// </summary>
    public short[] EjectorBackwardSpeed { get; set; } = new short[4];

    /// <summary>
    ///     托模进 1~4段位置
    /// </summary>
    public double[] EjectorForwardPosition { get; set; } = new double[4];

    /// <summary>
    ///     位置
    /// </summary>
    public double[] EjectorBackwardPosition { get; set; } = new double[4];

    /// <summary>
    ///     托模进延时时间
    /// </summary>
    public double EjectorForwardDelayTime { get; set; }

    /// <summary>
    ///     托模退延时时间
    /// </summary>
    public double EjectorBackwardDelayTime { get; set; }

    #endregion F5F2EjectorSetting（托模设定F5F2）

    #region 座台设置

    /// <summary>
    /// 座台进动作时间
    /// </summary>
    public double SeatAdvanceActionTime { get; set; }

    /// <summary>
    /// 座台进一段压力
    /// </summary>
    public short CoreAForwardPressure1 { get; set; }

    /// <summary>
    /// 座台进一段速度
    /// </summary>
    public short CoreAForwardSpeed1 { get; set; }
    
    /// <summary>
    /// 座台进二段压力
    /// </summary>
    public short CoreAForwardPressure2 { get; set; }
    
    /// <summary>
    /// 座台进二段速度
    /// </summary>
    public short CoreAForwardSpeed2 { get; set; }
    
    /// <summary>
    /// 座台退一段压力
    /// </summary>
    public short CoreAReversePressure1 { get; set; }
    
    /// <summary>
    /// 座台退一段速度
    /// </summary>
    public short CoreAReverseSpeed1 { get; set; }
    
    /// <summary>
    /// 座台退动作时间
    /// </summary>
    public short CoreAReverseTime { get; set; }
    
    #endregion
    
    #region F6F2CoreSetting（中子设定F6F2）

    /// <summary>
    ///     中子A进动作位置
    /// </summary>
    public double CoreAForwardPosition { get; set; }

    /// <summary>
    ///     中子A进压力
    /// </summary>
    public short CoreAForwardPressure { get; set; }

    /// <summary>
    ///     中子A进速度
    /// </summary>
    public short CoreAForwardSpeed { get; set; }

    /// <summary>
    ///     中子A进动作时间
    /// </summary>
    public double CoreAForwardTime { get; set; }

    /// <summary>
    ///     中子A进绞牙计数
    /// </summary>
    public short CoreAForwardCount { get; set; }

    /// <summary>
    ///     中子A退动作位置
    /// </summary>
    public double CoreABackwardPosition { get; set; }

    /// <summary>
    ///     中子A退压力
    /// </summary>
    public short CoreABackwardPressure { get; set; }

    /// <summary>
    ///     中子A退速度
    /// </summary>
    public short CoreABackwardSpeed { get; set; }

    /// <summary>
    ///     中子A退动作时间
    /// </summary>
    public double CoreABackwardTime { get; set; }

    /// <summary>
    ///     中子A退绞牙计数
    /// </summary>
    public short CoreABackwardCount { get; set; }

    /// <summary>
    ///     中子A退绞牙退二
    /// </summary>
    public short CoreABackward2 { get; set; }

    /// <summary>
    ///     中子B进动作位置
    /// </summary>
    public double CoreBForwardPosition { get; set; }

    /// <summary>
    ///     中子B进压力
    /// </summary>
    public short CoreBForwardPressure { get; set; }

    /// <summary>
    ///     中子B进速度
    /// </summary>
    public short CoreBForwardSpeed { get; set; }

    /// <summary>
    ///     中子B进动作时间
    /// </summary>
    public double CoreBForwardTime { get; set; }

    /// <summary>
    ///     中子B进绞牙计数
    /// </summary>
    public short CoreBForwardCount { get; set; }

    /// <summary>
    ///     中子B退动作位置
    /// </summary>
    public double CoreBBackwardPosition { get; set; }

    /// <summary>
    ///     中子B退压力
    /// </summary>
    public short CoreBBackwardPressure { get; set; }

    /// <summary>
    ///     中子B退速度
    /// </summary>
    public short CoreBBackwardSpeed { get; set; }

    /// <summary>
    ///     中子B退动作时间
    /// </summary>
    public double CoreBBackwardTime { get; set; }

    /// <summary>
    ///     中子B退绞牙计数
    /// </summary>
    public short CoreBBackwardCount { get; set; }

    /// <summary>
    ///     中子C进动作位置
    /// </summary>
    public double CoreCForwardPosition { get; set; }

    /// <summary>
    ///     中子C进压力
    /// </summary>
    public short CoreCForwardPressure { get; set; }

    /// <summary>
    ///     中子C进速度
    /// </summary>
    public short CoreCForwardSpeed { get; set; }

    /// <summary>
    ///     中子C进动作时间
    /// </summary>
    public double CoreCForwardTime { get; set; }

    /// <summary>
    ///     中子C进绞牙计数
    /// </summary>
    public short CoreCForwardCount { get; set; }

    /// <summary>
    ///     中子C退动作位置
    /// </summary>
    public double CoreCBackwardPosition { get; set; }

    /// <summary>
    ///     中子C退压力
    /// </summary>
    public short CoreCBackwardPressure { get; set; }

    /// <summary>
    ///     中子C退速度
    /// </summary>
    public short CoreCBackwardSpeed { get; set; }

    /// <summary>
    ///     中子C退动作时间
    /// </summary>
    public double CoreCBackwardTime { get; set; }

    /// <summary>
    ///     中子C退绞牙计数
    /// </summary>
    public short CoreCBackwardCount { get; set; }

    /// <summary>
    ///     中子D进动作位置
    /// </summary>
    public double CoreDForwardPosition { get; set; }

    /// <summary>
    ///     中子D进压力
    /// </summary>
    public short CoreDForwardPressure { get; set; }

    /// <summary>
    ///     中子D进速度
    /// </summary>
    public short CoreDForwardSpeed { get; set; }

    /// <summary>
    ///     中子D进动作时间
    /// </summary>
    public double CoreDForwardTime { get; set; }

    /// <summary>
    ///     中子D进绞牙计数
    /// </summary>
    public short CoreDForwardCount { get; set; }

    /// <summary>
    ///     中子D退动作位置
    /// </summary>
    public double CoreDBackwardPosition { get; set; }

    /// <summary>
    ///     中子D退压力
    /// </summary>
    public short CoreDBackwardPressure { get; set; }

    /// <summary>
    ///     中子D退速度
    /// </summary>
    public short CoreDBackwardSpeed { get; set; }

    /// <summary>
    ///     中子D退动作时间
    /// </summary>
    public double CoreDBackwardTime { get; set; }

    /// <summary>
    ///     中子D退绞牙计数
    /// </summary>
    public short CoreDBackwardCount { get; set; }

    #endregion F6F2CoreSetting（中子设定F6F2）
    
}
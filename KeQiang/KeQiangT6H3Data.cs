namespace Reception.KeQiang;

/// <summary>
///     科强T5H3注塑机
/// </summary>
public class KeQiangT6H3Data
{
    #region 控制器发出的数据

    /// <summary>
    /// 模数
    /// </summary>
    public int AnalogToDigital { get; set; }
    
    /// <summary>
    /// Pbar
    /// </summary>
    public int Pbar { get; set; }
    
    /// <summary>
    /// Fbar
    /// </summary>
    public int Fbar { get; set; }

    /// <summary>
    /// BP
    /// </summary>
    public int BP { get; set; }
    /// <summary>
    /// 油温
    /// </summary>
    public int OilTemperature { get; set; }

    /// <summary>
    /// 温度1段
    /// </summary>
    public int TemperatureSegment1 { get; set; }

    /// <summary>
    /// 温度2段
    /// </summary>
    public int TemperatureSegment2 { get; set; }

    /// <summary>
    /// 温度3段
    /// </summary>
    public int TemperatureSegment3 { get; set; }

    /// <summary>
    /// 温度4段
    /// </summary>
    public int TemperatureSegment4 { get; set; }

    /// <summary>
    /// 温度5段
    /// </summary>
    public int TemperatureSegment5 { get; set; }

    /// <summary>
    /// 温度6段
    /// </summary>
    public int TemperatureSegment6 { get; set; }

    /// <summary>
    /// 温度7段
    /// </summary>
    public int TemperatureSegment7 { get; set; }

    /// <summary>
    /// 周期时间
    /// </summary>
    public int CycleTime { get; set; }

    #endregion

    #region 开机的数据

    #region 模座数据

    /// <summary>
    /// 合模压力1
    /// </summary>
    public double ClosingPressure1 { get; set; }

    /// <summary>
    /// 合模速度1
    /// </summary>
    public double ClosingSpeed1 { get; set; }

    /// <summary>
    /// 合模压力2
    /// </summary>
    public double ClosingPressure2 { get; set; }

    /// <summary>
    /// 合模速度2
    /// </summary>
    public double ClosingSpeed2 { get; set; }

    /// <summary>
    /// 合模位置2
    /// </summary>
    public double ClosingPosition2 { get; set; }

    /// <summary>
    /// 合模压力3
    /// </summary>
    public double ClosingPressure3 { get; set; }

    /// <summary>
    /// 合模速度3
    /// </summary>
    public double ClosingSpeed3 { get; set; }

    /// <summary>
    /// 合模位置3
    /// </summary>
    public double ClosingPosition3 { get; set; }

    /// <summary>
    /// 合模压力4
    /// </summary>
    public double ClosingPressure4 { get; set; }

    /// <summary>
    /// 合模速度4
    /// </summary>
    public double ClosingSpeed4 { get; set; }

    /// <summary>
    /// 合模位置4
    /// </summary>
    public double ClosingPosition4 { get; set; }

    /// <summary>
    /// 合模压力5
    /// </summary>
    public double ClosingPressure5 { get; set; }

    /// <summary>
    /// 合模速度5
    /// </summary>
    public double ClosingSpeed5 { get; set; }

    /// <summary>
    /// 合模位置5
    /// </summary>
    public double ClosingPosition5 { get; set; }

    /// <summary>
    /// 开模压力1
    /// </summary>
    public double OpeningPressure1 { get; set; }

    /// <summary>
    /// 开模速度1
    /// </summary>
    public double OpeningSpeed1 { get; set; }

    /// <summary>
    /// 开模压力2
    /// </summary>
    public double OpeningPressure2 { get; set; }

    /// <summary>
    /// 开模速度2
    /// </summary>
    public double OpeningSpeed2 { get; set; }

    /// <summary>
    /// 开模位置1
    /// </summary>
    public double OpeningPosition1 { get; set; }

    /// <summary>
    /// 开模压力3
    /// </summary>
    public double OpeningPressure3 { get; set; }

    /// <summary>
    /// 开模速度3
    /// </summary>
    public double OpeningSpeed3 { get; set; }

    /// <summary>
    /// 开模位置2
    /// </summary>
    public double OpeningPosition2 { get; set; }

    /// <summary>
    /// 开模压力4
    /// </summary>
    public double OpeningPressure4 { get; set; }

    /// <summary>
    /// 开模速度4
    /// </summary>
    public double OpeningSpeed4 { get; set; }

    /// <summary>
    /// 开模位置3
    /// </summary>
    public double OpeningPosition3 { get; set; }

    /// <summary>
    /// 开模压力5
    /// </summary>
    public double OpeningPressure5 { get; set; }

    /// <summary>
    /// 开模速度5
    /// </summary>
    public double OpeningSpeed5 { get; set; }

    /// <summary>
    /// 开模位置4
    /// </summary>
    public double OpeningPosition4 { get; set; }

    /// <summary>
    /// 开模行程
    /// </summary>
    public double OpeningStroke { get; set; }

    /// <summary>
    /// 合模位置1
    /// </summary>
    public double ClosingPosition1 { get; set; }

    /// <summary>
    /// 开模位置5
    /// </summary>
    public double OpeningPosition5 { get; set; }

    /// <summary>
    /// 注射压力1
    /// </summary>
    public double InjectionPressure1 { get; set; }

    /// <summary>
    /// 注射速度1
    /// </summary>
    public double InjectionSpeed1 { get; set; }

    /// <summary>
    /// 注射压力2
    /// </summary>
    public double InjectionPressure2 { get; set; }

    /// <summary>
    /// 注射速度2
    /// </summary>
    public double InjectionSpeed2 { get; set; }

    /// <summary>
    /// 注射位置1
    /// </summary>
    public double InjectionPosition1 { get; set; }

    /// <summary>
    /// 注射压力3
    /// </summary>
    public double InjectionPressure3 { get; set; }

    /// <summary>
    /// 注射速度3
    /// </summary>
    public double InjectionSpeed3 { get; set; }

    /// <summary>
    /// 注射位置2
    /// </summary>
    public double InjectionPosition2 { get; set; }

    /// <summary>
    /// 注射压力4
    /// </summary>
    public double InjectionPressure4 { get; set; }

    /// <summary>
    /// 注射速度4
    /// </summary>
    public double InjectionSpeed4 { get; set; }

    /// <summary>
    /// 注射位置3
    /// </summary>
    public double InjectionPosition3 { get; set; }

    /// <summary>
    /// 注射压力5
    /// </summary>
    public double InjectionPressure5 { get; set; }

    /// <summary>
    /// 注射速度5
    /// </summary>
    public double InjectionSpeed5 { get; set; }

    /// <summary>
    /// 注射位置4
    /// </summary>
    public double InjectionPosition4 { get; set; }

    /// <summary>
    /// 注射压力6
    /// </summary>
    public double InjectionPressure6 { get; set; }

    /// <summary>
    /// 注射速度6
    /// </summary>
    public double InjectionSpeed6 { get; set; }

    /// <summary>
    /// 注射位置5
    /// </summary>
    public double InjectionPosition5 { get; set; }

    /// <summary>
    /// 射退压力1
    /// </summary>
    public double RetractionPressure1 { get; set; }

    /// <summary>
    /// 射退速度1
    /// </summary>
    public double RetractionSpeed1 { get; set; }

    /// <summary>
    /// 射退时间1
    /// </summary>
    public double RetractionTime1 { get; set; }

    /// <summary>
    /// 射退压力2
    /// </summary>
    public double RetractionPressure2 { get; set; }

    /// <summary>
    /// 射退速度2
    /// </summary>
    public double RetractionSpeed2 { get; set; }

    /// <summary>
    /// 射退时间1
    /// </summary>
    public double RetractionTime2 { get; set; }

    /// <summary>
    /// 储料压力5
    /// </summary>
    public double HoldingPressure5 { get; set; }

    /// <summary>
    /// 储料速度5
    /// </summary>
    public double HoldingSpeed5 { get; set; }

    /// <summary>
    /// 储料背压5
    /// </summary>
    public double HoldingBackPressure5 { get; set; }

    /// <summary>
    /// 储料压力4
    /// </summary>
    public double HoldingPressure4 { get; set; }

    /// <summary>
    /// 储料速度4
    /// </summary>
    public double HoldingSpeed4 { get; set; }

    /// <summary>
    /// 储料背压4
    /// </summary>
    public double HoldingBackPressure4 { get; set; }

    /// <summary>
    /// 储料位置4
    /// </summary>
    public double HoldingPosition4 { get; set; }

    /// <summary>
    /// 储料位置5
    /// </summary>
    public double HoldingPosition5 { get; set; }

    /// <summary>
    /// 储料压力
    /// </summary>
    public double HoldingPressure { get; set; }

    /// <summary>
    /// 储料速度
    /// </summary>
    public double HoldingSpeed { get; set; }

    /// <summary>
    /// 储料距离
    /// </summary>
    public double HoldingDistance { get; set; }

    /// <summary>
    /// 顶进压力1
    /// </summary>
    public double TopInPressure1 { get; set; }

    /// <summary>
    /// 顶进速度1
    /// </summary>
    public double TopInSpeed1 { get; set; }

    /// <summary>
    /// 顶进压力2
    /// </summary>
    public double TopInPressure2 { get; set; }

    /// <summary>
    /// 顶进速度2
    /// </summary>
    public double TopInSpeed2 { get; set; }

    /// <summary>
    /// 顶进位置1
    /// </summary>
    public double TopInPosition1 { get; set; }

    /// <summary>
    /// 顶进位置2
    /// </summary>
    public double TopInPosition2 { get; set; }

    /// <summary>
    /// 顶退压力1
    /// </summary>
    public double TopOutPressure1 { get; set; }

    /// <summary>
    /// 顶退速度1
    /// </summary>
    public double TopOutSpeed1 { get; set; }

    /// <summary>
    /// 顶退压力2
    /// </summary>
    public double TopOutPressure2 { get; set; }

    #endregion

    #region 34 05 13

    /// <summary>
    /// 顶退速度2
    /// </summary>
    public double TopOutSpeed2 { get; set; }

    /// <summary>
    /// 顶退位置1
    /// </summary>
    public double TopOutPosition1 { get; set; }

    /// <summary>
    /// 顶退位置2
    /// </summary>
    public double TopOutPosition2 { get; set; }

    /// <summary>
    /// 座台进压力1
    /// </summary>
    public double BedInPressure1 { get; set; }

    /// <summary>
    /// 座台进速度1
    /// </summary>
    public double BedInSpeed1 { get; set; }

    /// <summary>
    /// 座台进时间1
    /// </summary>
    public double BedInTime1 { get; set; }

    /// <summary>
    /// 座台进压力2
    /// </summary>
    public double BedInPressure2 { get; set; }

    /// <summary>
    /// 座台进速度2
    /// </summary>
    public double BedInSpeed2 { get; set; }

    /// <summary>
    /// 座台进时间2
    /// </summary>
    public double BedInTime2 { get; set; }

    /// <summary>
    /// 座台进压力3
    /// </summary>
    public double BedInPressure3 { get; set; }

    /// <summary>
    /// 座台进速度3
    /// </summary>
    public double BedInSpeed3 { get; set; }

    /// <summary>
    /// 座台进时间3
    /// </summary>
    public double BedInTime3 { get; set; }

    /// <summary>
    /// 座台退压力1
    /// </summary>
    public double BedOutPressure1 { get; set; }

    /// <summary>
    /// 座台退速度1
    /// </summary>
    public double BedOutSpeed1 { get; set; }

    /// <summary>
    /// 座台退时间1
    /// </summary>
    public double BedOutTime1 { get; set; }

    /// <summary>
    /// 调模退压力
    /// </summary>
    public double MoldOutPressure { get; set; }

    /// <summary>
    /// 调模退速度
    /// </summary>
    public double MoldOutSpeed { get; set; }

    /// <summary>
    /// 调模进压力
    /// </summary>
    public double MoldInPressure { get; set; }

    /// <summary>
    /// 调模进速度
    /// </summary>
    public double MoldInSpeed { get; set; }

    /// <summary>
    /// 调模进慢速
    /// </summary>
    public double MoldInSlowSpeed { get; set; }

    #endregion
    #endregion

}
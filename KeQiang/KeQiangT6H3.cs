using System.IO.Ports;
using System.Text;
using System.Text.RegularExpressions;
using HslCommunication;
using HslCommunication.BasicFramework;
using HslCommunication.LogNet;
using Newtonsoft.Json;

namespace Reception.KeQiang;

public class KeQiangT6H3 : IDisposable
{
    private readonly SerialPort _serialPort;
    private readonly CancellationTokenSource _tokenSource = new();
    private readonly StringBuilder _receivedData = new();
    private readonly ILogNet logNet;
    private readonly bool init;
    private readonly KeQiangT6H3Data _hxData = new();
    private readonly string deviceName = "/Edge/KeQiangT6H3.txt";

    public KeQiangT6H3(string portName)
    {
        try
        {
            logNet = new LogNetDateTime("./logs", GenerateMode.ByEveryDay, 7);
            logNet.WriteInfo($"KeQiang_T6H3 初始化--串口:{portName},波特率:{115200},数据位:{8},停止位:{1},校验位:{0}");

            #region TCP 测试

            // _tcpClient = new TcpClient();
            // _tcpClient.Connect("127.0.0.1", 5027);
            // _receivedData.Clear();
            // var receiveThread = new Thread(ReceiveData);
            // receiveThread.Start();

            #endregion

            _serialPort = new SerialPort();
            _serialPort.PortName = portName;
            _serialPort.BaudRate = 115200;
            _serialPort.DataBits = 8;
            _serialPort.StopBits = StopBits.One;
            _serialPort.Parity = Parity.Odd;
            _serialPort.Open();

            logNet.WriteInfo("KeQiang_T6H3 串口已开启 等待接受中······");
            var receiveThread = new Thread(SerialReceiveData);
            receiveThread.Start();

            if (File.Exists(deviceName))
            {
                var data = File.ReadAllText(deviceName);
                if (!string.IsNullOrEmpty(data))
                {
                    var hxData = JsonConvert.DeserializeObject<KeQiangT6H3Data?>(data);
                    if (hxData != null)
                        _hxData = hxData;
                }
            }

            // 标记已经初始化完成，防止文件内容互相刷新
            init = true;

            // logNet.WriteInfo($"KeQiang_T6H3 【数据】：{_hxData.ToJsonString()}");
            Thread.Sleep(1000 * 30);
            _tokenSource.Cancel();
        }
        catch (Exception ex)
        {
            logNet.WriteError("【KeQiang_T6H3】 初始化异常：" + ex.Message);
        }
        finally
        {
            logNet.WriteInfo($"KeQiang_T6H3 写入路径：{deviceName} ,值：{_hxData.ToJsonString()}");
            File.WriteAllText(deviceName, _hxData.ToJsonString());
            logNet.WriteInfo("KeQiang_T6H3 任务已经取消!");
        }
    }

    public void Dispose()
    {
        _serialPort?.Close();
        _serialPort?.Dispose();
    }


    //private TcpClient _tcpClient;


    // private void ReceiveData()
    // {
    //     var stream = _tcpClient.GetStream();
    //     while (!_tokenSource.IsCancellationRequested)
    //     {
    //         try
    //         {
    //             var buffer = new byte[10240];
    //             var bytesRead = stream.Read(buffer, 0, buffer.Length);
    //             if (bytesRead > 0)
    //             {
    //                 var resizedBuffer = new byte[bytesRead];
    //                 Array.Copy(buffer, resizedBuffer, resizedBuffer.Length);
    //                 buffer = resizedBuffer;
    //                 var data = SoftBasic.ByteToHexString(buffer, ' ');
    //
    //                 logNet.WriteInfo($"时间：{DateTime.Now}\t【收】:" + data);
    //                 if (_receivedData.Length > 0)
    //                     _receivedData.Append(' ');
    //                 _receivedData.Append(data);
    //                 // 初始化完成在开始解析
    //                 if (init)
    //                     ProcessMessages();
    //             }
    //         }
    //         catch (Exception ex)
    //         {
    //             if (!_tcpClient.Connected) break;
    //         }
    //
    //         Thread.Sleep(20);
    //     }
    // }

    private void SerialReceiveData()
    {
        var stream = _serialPort;
        while (!_tokenSource.IsCancellationRequested)
            try
            {
                var buffer = new byte[10240];
                var bytesRead = stream.Read(buffer, 0, buffer.Length);
                if (bytesRead > 0)
                {
                    var resizedBuffer = new byte[bytesRead];
                    Array.Copy(buffer, resizedBuffer, resizedBuffer.Length);
                    buffer = resizedBuffer;
                    var data = SoftBasic.ByteToHexString(buffer, ' ');

                    logNet.WriteInfo($"时间：{DateTime.Now}\t【收】:" + data);
                    if (_receivedData.Length > 0)
                        _receivedData.Append(' ');
                    _receivedData.Append(data);
                    // 初始化完成在开始解析
                    if (init)
                        ProcessMessages();
                }
            }
            catch (Exception ex)
            {
                logNet.WriteError("【收】 接收数据发生错误:" + ex.Message);
                break;
            }
    }

    /// <summary>
    ///     解析报文
    /// </summary>
    private void ProcessMessages()
    {
        var startIndex = -1;
        var data = _receivedData.ToString();
        try
        {
            // 使用正则表达式匹配符合条件的报文头
            var match = Regex.Match(data, "(F6 6F 04 05 13|F6 6F 74 05 13)");
            if (!match.Success)
                return;
            // 获取符合条件的报文头的索引位置
            startIndex = match.Index;
            // 移除不符合条件的报文
            data = data.Remove(0, startIndex);
            // 移除后的数据是否符合报文起始
            if (!data.StartsWith("F6 6F"))
                return;
        }
        catch (Exception e)
        {
        }

        var parts = data.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        var bytes = new byte[parts.Length];
        for (var i = 0; i < parts.Length; i++) bytes[i] = Convert.ToByte(parts[i], 16);

        while (startIndex != -1 && !_tokenSource.IsCancellationRequested)
            try
            {
                if (data.StartsWith("F6 6F 74 05 13"))
                {
                    DealData24(bytes);
                    bytes = RemoveBytesFromArray(bytes, 0, bytes.Length);
                    // 从接收缓冲区中移除已解析的报文
                    data = _receivedData.Remove(0, 3104).ToString();
                }
                else if (data.StartsWith("F6 6F 04 05 13"))
                {
                    DealData34(bytes);
                    bytes = RemoveBytesFromArray(bytes, 0, bytes.Length);
                    // 从接收缓冲区中移除已解析的报文
                    data = _receivedData.Remove(0, 3104).ToString();
                }
                else
                {
                    return;
                }
            }
            catch (Exception e)
            {
                Console.WriteLine($"【KeQiangT6H3】 value:【{SoftBasic.ByteToHexString(bytes, ' ')}】 报文解析 Error: " + e.Message);
                bytes = RemoveBytesFromArray(bytes, 0, bytes.Length);
                // 移除不符合条件报文
                data = data.Remove(0, startIndex == 0 ? 1 : startIndex);
                startIndex = -1;
            }
    }

    private byte[] RemoveBytesFromArray(byte[] source, int startIndex, int length)
    {
        var destination = new byte[source.Length - length];
        Array.Copy(source, 0, destination, 0, startIndex);
        Array.Copy(source, startIndex + length, destination, startIndex, source.Length - startIndex - length);
        return destination;
    }

    /// <summary>
    ///     小数保留位数
    /// </summary>
    private const int RoundLength = 1;

    /// <summary>
    ///     开机的数据 F6 6F 74 05 13
    ///     模座数据
    /// </summary>
    /// <param name="buff"></param>
    private void DealData24(byte[] buff)
    {
        _hxData.ClosingPressure1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 265, 4)) * 0.01, RoundLength);
        _hxData.ClosingSpeed1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 269, 4)) * 0.01, RoundLength);
        _hxData.ClosingPressure2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 273, 4)) * 0.01, RoundLength);
        _hxData.ClosingSpeed2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 277, 4)) * 0.01, RoundLength);
        // 合模位置2
        _hxData.ClosingPosition2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 281, 4)) * 0.001, RoundLength);
        _hxData.ClosingPressure3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 285, 4)) * 0.01, RoundLength);
        _hxData.ClosingSpeed3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 289, 4)) * 0.01, RoundLength);
        // 合模位置3
        _hxData.ClosingPosition3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 293, 4)) * 0.001, RoundLength);
        _hxData.ClosingPressure4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 297, 4)) * 0.01, RoundLength);
        _hxData.ClosingSpeed4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 301, 4)) * 0.01, RoundLength);
        //合模位置4
        _hxData.ClosingPosition4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 305, 4)) * 0.001, RoundLength);
        _hxData.ClosingPressure5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 309, 4)) * 0.01, RoundLength);
        _hxData.ClosingSpeed5 = Math.Round(BitConverter.ToInt16(GetHexStringFromByteArray(buff, 313, 4)) * 0.01, RoundLength);
        // 合模位置5
        _hxData.ClosingPosition5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 317, 4)) * 0.001, RoundLength);
        _hxData.OpeningPressure1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 345, 4)) * 0.01, RoundLength);
        _hxData.OpeningSpeed1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 349, 4)) * 0.01, RoundLength);
        _hxData.OpeningPressure2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 353, 4)) * 0.01, RoundLength);
        _hxData.OpeningSpeed2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 357, 4)) * 0.01, RoundLength);
        // 开模位置1
        _hxData.OpeningPosition1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 361, 4)) * 0.001, RoundLength);
        _hxData.OpeningPressure3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 365, 4)) * 0.01, RoundLength);
        _hxData.OpeningSpeed3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 369, 4)) * 0.01, RoundLength);
        //
        _hxData.OpeningPosition2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 373, 4)) * 0.001, RoundLength);
        _hxData.OpeningPressure4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 377, 4)) * 0.01, RoundLength);
        _hxData.OpeningSpeed4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 381, 4)) * 0.01, RoundLength);
        //
        _hxData.OpeningPosition3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 385, 4)) * 0.001, RoundLength);
        _hxData.OpeningPressure5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 389, 4)) * 0.01, RoundLength);
        _hxData.OpeningSpeed5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 393, 4)) * 0.01, RoundLength);
        //
        _hxData.OpeningPosition4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 397, 4)) * 0.001, RoundLength);
        _hxData.OpeningStroke = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 401, 4)) * 0.01, RoundLength);
        _hxData.ClosingPosition1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 401, 4)) * 0.01, RoundLength);
        //
        _hxData.OpeningPosition5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 401, 4)) * 0.001, RoundLength);
        _hxData.InjectionPressure1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 441, 4)) * 0.01, RoundLength);
        _hxData.InjectionSpeed1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 445, 4)) * 0.01, RoundLength);
        _hxData.InjectionPressure2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 449, 4)) * 0.01, RoundLength);
        _hxData.InjectionSpeed2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 453, 4)) * 0.01, RoundLength);
        // 注射位置1
        _hxData.InjectionPosition1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 457, 4)) * 0.001, RoundLength);
        _hxData.InjectionPressure3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 361, 4)) * 0.01, RoundLength);
        _hxData.InjectionSpeed3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 465, 4)) * 0.01, RoundLength);
        //
        _hxData.InjectionPosition2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 469, 4)) * 0.001, RoundLength);
        _hxData.InjectionPressure4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 473, 4)) * 0.01, RoundLength);
        _hxData.InjectionSpeed4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 477, 5)) * 0.01, RoundLength);
        // 
        _hxData.InjectionPosition3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 481, 4)) * 0.001, RoundLength);
        _hxData.InjectionPressure5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 485, 5)) * 0.01, RoundLength);
        _hxData.InjectionSpeed5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 489, 5)) * 0.01, RoundLength);
        //
        _hxData.InjectionPosition4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 493, 4)) * 0.001, RoundLength);
        _hxData.InjectionPressure6 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 497, 4)) * 0.01, RoundLength);
        _hxData.InjectionSpeed6 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 501, 4)) * 0.01, RoundLength);
        // 
        _hxData.InjectionPosition5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 505, 4)) * 0.001, RoundLength);
        _hxData.RetractionPressure1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 573, 4)) * 0.01, RoundLength);
        _hxData.RetractionSpeed1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 577, 4)) * 0.01, RoundLength);
        _hxData.RetractionTime1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 581, 4)) * 0.01, RoundLength);
        _hxData.RetractionPressure2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 585, 4)) * 0.01, RoundLength);
        _hxData.RetractionSpeed2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 589, 4)) * 0.01, RoundLength);
        _hxData.RetractionTime2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 593, 4)) * 0.01, RoundLength);
        _hxData.HoldingPressure5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 781, 4)) * 0.01, RoundLength);
        _hxData.HoldingSpeed5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 785, 4)) * 0.01, RoundLength);
        _hxData.HoldingBackPressure5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 789, 4)) * 0.01, RoundLength);
        _hxData.HoldingPressure4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 797, 4)) * 0.01, RoundLength);
        _hxData.HoldingSpeed4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 801, 4)) * 0.01, RoundLength);
        _hxData.HoldingBackPressure4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 805, 4)) * 0.01, RoundLength);
        // 储料位置4
        _hxData.HoldingPosition4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 809, 4)) * 0.001, RoundLength);
        // 储料位置5
        _hxData.HoldingPosition5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 893, 4)) * 0.001, RoundLength);
        _hxData.HoldingPressure = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 937, 4)) * 0.01, RoundLength);
        // 储料速度
        _hxData.HoldingSpeed = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 941, 4)) * 0.001, RoundLength);
        // 储料距离
        _hxData.HoldingDistance = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 945, 4)) * 0.001, RoundLength);
        _hxData.TopInPressure1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 949, 4)) * 0.01, RoundLength);
        _hxData.TopInSpeed1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 997, 4)) * 0.01, RoundLength);
        _hxData.TopInPressure2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 1001, 4)) * 0.01, RoundLength);
        _hxData.TopInSpeed2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 1005, 4)) * 0.01, RoundLength);
        // 顶进位置1
        _hxData.TopInPosition1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 1009, 4)) * 0.001, RoundLength);
        // 顶进位置2
        _hxData.TopInPosition2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 1013, 4)) * 0.001, RoundLength);
        _hxData.TopOutPressure1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 1017, 4)) * 0.01, RoundLength);
        _hxData.TopOutSpeed1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 1021, 4)) * 0.01, RoundLength);
        _hxData.TopOutPressure2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 1025, 4)) * 0.01, RoundLength);
    }

    /// <summary>
    ///     开机的数据 F6 6F 04 05 13
    /// </summary>
    /// <param name="buff"></param>
    private void DealData34(byte[] buff)
    {
        // 34 05 13
        _hxData.TopOutSpeed2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 9, 4)) * 0.01, RoundLength);
        // 顶退位置1
        _hxData.TopOutPosition1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 13, 4)) * 0.001, RoundLength);
        // 顶退位置2
        _hxData.TopOutPosition2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 17, 4)) * 0.001, RoundLength);
        _hxData.BedInPressure1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 109, 4)) * 0.01, RoundLength);
        _hxData.BedInSpeed1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 113, 4)) * 0.01, RoundLength);

        _hxData.BedInTime1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 117, 4)) * 0.01, RoundLength);
        _hxData.BedInPressure2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 121, 4)) * 0.01, RoundLength);
        _hxData.BedInSpeed2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 125, 4)) * 0.01, RoundLength);
        _hxData.BedInTime2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 129, 4)) * 0.01, RoundLength);
        _hxData.BedInPressure3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 133, 4)) * 0.01, RoundLength);
        _hxData.BedInSpeed3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 137, 4)) * 0.01, RoundLength);
        _hxData.BedInTime3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 141, 4)) * 0.01, RoundLength);

        _hxData.BedOutPressure1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 145, 4)) * 0.01, RoundLength);
        _hxData.BedOutSpeed1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 149, 4)) * 0.01, RoundLength);
        _hxData.BedOutTime1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 169, 4)) * 0.01, RoundLength);
        _hxData.MoldOutPressure = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 193, 4)) * 0.01, RoundLength);
        _hxData.MoldOutSpeed = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 197, 4)) * 0.01, RoundLength);
        _hxData.MoldInPressure = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 201, 4)) * 0.01, RoundLength);
        _hxData.MoldInSpeed = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 205, 4)) * 0.01, RoundLength);
        _hxData.MoldInSlowSpeed = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 209, 4)) * 0.01, RoundLength);
    }

    /// <summary>
    ///     查找数组中下标
    /// </summary>
    /// <param name="byteArray"></param>
    /// <param name="sequence"></param>
    /// <returns></returns>
    private static List<int> FindIndex(byte[] byteArray, byte[] sequence)
    {
        var indexes = new List<int>();
        for (var i = 0; i <= byteArray.Length - sequence.Length; i++)
        {
            var found = true;
            for (var j = 0; j < sequence.Length; j++)
                if (byteArray[i + j] != sequence[j])
                {
                    found = false;
                    break;
                }

            if (found) indexes.Add(i);
        }

        return indexes;
    }

    /// <summary>
    /// </summary>
    /// <param name="byteArray"></param>
    /// <param name="startIndex"></param>
    /// <param name="length"></param>
    /// <returns></returns>
    private static byte[] GetHexStringFromByteArray(byte[] byteArray, int startIndex, int length)
    {
        var subData = new byte[length];
        Array.Copy(byteArray, startIndex, subData, 0, length);
        //byte[] reversedData = SoftBasic.BytesReverseByWord(subData);
        return subData;
    }
}
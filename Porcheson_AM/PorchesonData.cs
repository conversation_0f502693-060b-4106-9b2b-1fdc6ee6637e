namespace PORCHESON_Ps660Am;

/// <summary>
///  宝捷信 注塑机字段
/// </summary>
public class PorchesonData
{
    #region 上电数据

    /// <summary>
    /// 慢速合模压力 
    /// </summary>
    public double SlowCombinePressure { get; set; }

    /// <summary>
    ///     快速合模压力
    /// </summary>
    public double QuickCombinePressure { get; set; }
    
    /// <summary>
    ///     低压合模压力
    /// </summary>
    public double LowCombinePressure { get; set; }
    /// <summary>
    ///     高压合模压力
    /// </summary>
    public double HighCombinePressure { get; set; }
    
    /// <summary>
    /// 慢速合模流量
    /// </summary>
    public double SlowCombineFlow { get; set; }
    
    /// <summary>
    ///     快速合模流量
    /// </summary>
    public double QuickCombineFlow { get; set; }

    /// <summary>
    ///     低压合模流量
    /// </summary>
    public double LowCombineFlow { get; set; }

    /// <summary>
    ///     高压合模流量
    /// </summary>
    public double HighCombineFlow { get; set; }
    
    /// <summary>
    ///     低压保护时间
    /// </summary>
    public double LowPressureProtectionTime { get; set; }
    
    /// <summary>
    ///     开合模限时
    /// </summary>
    public double MoldClosingTimeLimit { get; set; }
    
    /// <summary>
    /// 差动锁模
    /// </summary>
    public int DifferentialModeLock { get; set; }

    /// <summary>
    /// 低速开模压力
    /// </summary>
    public double LowSpeedMoldPressure { get; set; }

    /// <summary>
    /// 中速开模压力
    /// </summary>
    public double MediumSpeedMoldPressure { get; set; }

    /// <summary>
    /// 快速开模压力
    /// </summary>
    public double FastSpeedMoldPressure { get; set; }

    /// <summary>
    /// 慢速开模压力
    /// </summary>
    public double SlowSpeedMoldPressure { get; set; }

    /// <summary>
    /// 低速开模流量
    /// </summary>
    public double LowSpeedMoldFlow { get; set; }

    /// <summary>
    /// 中速开模流量
    /// </summary>
    public double MediumSpeedMoldFlow { get; set; }

    /// <summary>
    /// 快速开模流量
    /// </summary>
    public double FastSpeedMoldFlow { get; set; }

    /// <summary>
    /// 慢速开模流量
    /// </summary>
    public double SlowSpeedMoldFlow { get; set; }
    
    /// <summary>
    /// 低速开模位置
    /// </summary>
    public double LowSpeedMoldPosition { get; set; }

    /// <summary>
    /// 中速开模位置
    /// </summary>
    public double MediumSpeedMoldPosition { get; set; }

    /// <summary>
    /// 快速开模位置
    /// </summary>
    public double FastSpeedMoldPosition { get; set; }

    /// <summary>
    /// 慢速开模位置
    /// </summary>
    public double SlowSpeedMoldPosition { get; set; }
    
    #endregion 上电数据

    #region 托模设定

    /// <summary>
    /// 托模退压力
    /// </summary>
    public double MoldRetreatPressure { get; set; }

    /// <summary>
    /// 托进快压力
    /// </summary>
    public double MoldAdvanceFastPressure { get; set; }

    /// <summary>
    /// 托进慢压力
    /// </summary>
    public double MoldAdvanceSlowPressure { get; set; }

    /// <summary>
    /// 托模保持压力
    /// </summary>
    public double MoldHoldPressure { get; set; }

    /// <summary>
    /// 托模退流量
    /// </summary>
    public double MoldRetreatFlow { get; set; }

    /// <summary>
    /// 托进快流量
    /// </summary>
    public double MoldAdvanceFastFlow { get; set; }

    /// <summary>
    /// 托进慢流量
    /// </summary>
    public double MoldAdvanceSlowFlow { get; set; }
    
    /// <summary>
    /// 托模退位置
    /// </summary>
    public double MoldRetreatPosition { get; set; }

    /// <summary>
    /// 托进快位置
    /// </summary>
    public double MoldAdvanceFastPosition { get; set; }

    /// <summary>
    /// 托进慢位置
    /// </summary>
    public double MoldAdvanceSlowPosition { get; set; }

    #endregion 座台/托模设定

    #region 射出设定

    /// <summary>
    /// 射出压力5
    /// </summary>
    public double InjectionPressure5 { get; set; }

    /// <summary>
    /// 射出压力4
    /// </summary>
    public double InjectionPressure4 { get; set; }

    /// <summary>
    /// 射出压力3
    /// </summary>
    public double InjectionPressure3 { get; set; }

    /// <summary>
    /// 射出压力2
    /// </summary>
    public double InjectionPressure2 { get; set; }

    /// <summary>
    /// 射出压力1
    /// </summary>
    public double InjectionPressure1 { get; set; }

    /// <summary>
    /// 射出流量5
    /// </summary>
    public double InjectionFlow5 { get; set; }

    /// <summary>
    /// 射出流量4
    /// </summary>
    public double InjectionFlow4 { get; set; }

    /// <summary>
    /// 射出流量3
    /// </summary>
    public double InjectionFlow3 { get; set; }

    /// <summary>
    /// 射出流量2
    /// </summary>
    public double InjectionFlow2 { get; set; }

    /// <summary>
    /// 射出流量1
    /// </summary>
    public double InjectionFlow1 { get; set; }

    /// <summary>
    /// 射出位置5
    /// </summary>
    public double InjectionPosition5 { get; set; }

    /// <summary>
    /// 射出位置4
    /// </summary>
    public double InjectionPosition4 { get; set; }

    /// <summary>
    /// 射出位置3
    /// </summary>
    public double InjectionPosition3 { get; set; }

    /// <summary>
    /// 射出位置2
    /// </summary>
    public double InjectionPosition2 { get; set; }

    /// <summary>
    /// 射出位置1
    /// </summary>
    public double InjectionPosition1 { get; set; }

    /// <summary>
    /// 射出总时
    /// </summary>
    public double InjectionTotalTime { get; set; }

    #endregion

    #region 坐台设定

    /// <summary>
    /// 座台进慢压力
    /// </summary>
    public double TableAdvanceSlowPressure { get; set; }

    /// <summary>
    /// 座台进快压力
    /// </summary>
    public double TableAdvanceFastPressure { get; set; }

    /// <summary>
    /// 座台退压力
    /// </summary>
    public double TableRetreatPressure { get; set; }

    /// <summary>
    /// 座台进慢流量
    /// </summary>
    public double TableAdvanceSlowFlow { get; set; }

    /// <summary>
    /// 座台进快流量
    /// </summary>
    public double TableAdvanceFastFlow { get; set; }

    /// <summary>
    /// 座台退流量
    /// </summary>
    public double TableRetreatFlow { get; set; }

    /// <summary>
    /// 座台进慢时间
    /// </summary>
    public double TableAdvanceSlowTime { get; set; }

    /// <summary>
    /// 座台进快时间
    /// </summary>
    public double TableAdvanceFastTime { get; set; }

    /// <summary>
    /// 座台退时间
    /// </summary>
    public double TableRetreatTime { get; set; }

    #endregion

    #region 储料、射退、冷却设定

    /// <summary>
    /// 储料前射退压力
    /// </summary>
    public double PreInjectionRetreatPressure { get; set; }

    /// <summary>
    /// 储料压力1
    /// </summary>
    public double MaterialPressure1 { get; set; }

    /// <summary>
    /// 储料压力2
    /// </summary>
    public double MaterialPressure2 { get; set; }

    /// <summary>
    /// 储料后射退压力
    /// </summary>
    public double PostInjectionRetreatPressure { get; set; }

    /// <summary>
    /// 储料前射退背压
    /// </summary>
    public double PreInjectionRetreatBackPressure { get; set; }

    /// <summary>
    /// 储料背压1
    /// </summary>
    public double MaterialBackPressure1 { get; set; }

    /// <summary>
    /// 储料背压2
    /// </summary>
    public double MaterialBackPressure2 { get; set; }

    /// <summary>
    /// 储料后射退背压
    /// </summary>
    public double PostInjectionRetreatBackPressure { get; set; }

    /// <summary>
    /// 储料前射退流量
    /// </summary>
    public double PreInjectionRetreatFlow { get; set; }

    /// <summary>
    /// 储料流量1
    /// </summary>
    public double MaterialFlow1 { get; set; }

    /// <summary>
    /// 储料流量2
    /// </summary>
    public double MaterialFlow2 { get; set; }

    /// <summary>
    /// 储料后射退流量
    /// </summary>
    public double PostInjectionRetreatFlow { get; set; }

    /// <summary>
    /// 储料前射退位置
    /// </summary>
    public double PreInjectionRetreatPosition { get; set; }

    /// <summary>
    /// 储料位置1
    /// </summary>
    public double MaterialPosition1 { get; set; }

    /// <summary>
    /// 储料位置2
    /// </summary>
    public double MaterialPosition2 { get; set; }

    /// <summary>
    /// 储料后射退位置
    /// </summary>
    public double PostInjectionRetreatPosition { get; set; }

    /// <summary>
    /// 冷却时间
    /// </summary>
    public double CoolingTime { get; set; }

    /// <summary>
    /// 储料限时
    /// </summary>
    public double MaterialLimitTime { get; set; }

    #endregion
    
    #region 计时/计数设定
    /// <summary>
    /// 润滑模数
    /// </summary>
    public int LubricationCycle { get; set; }

    /// <summary>
    /// 循环等待时间
    /// </summary>
    public double CycleWaitTime { get; set; }

    /// <summary>
    /// 润滑总时
    /// </summary>
    public double TotalLubricationTime { get; set; }

    /// <summary>
    /// 手动动作限时
    /// </summary>
    public double ManualActionTimeLimit { get; set; }

    /// <summary>
    /// 润滑时间
    /// </summary>
    public double LubricationTime { get; set; }

    /// <summary>
    /// 故障告警时间
    /// </summary>
    public double FaultWarningTime { get; set; }

    /// <summary>
    /// 润滑间歇
    /// </summary>
    public double LubricationInterval { get; set; }

    /// <summary>
    /// 周期时间
    /// </summary>
    public double CycleTime { get; set; }
    
    #endregion 计时/计数设定

    #region 温度设定

    /// <summary>
    ///     温度设定1
    /// </summary>
    public double TemperatureSetting1 { get; set; }

    /// <summary>
    ///     温度设定2
    /// </summary>
    public double TemperatureSetting2 { get; set; }

    /// <summary>
    ///     温度设定3
    /// </summary>
    public double TemperatureSetting3 { get; set; }

    /// <summary>
    ///     温度设定4
    /// </summary>
    public double TemperatureSetting4 { get; set; }

    /// <summary>
    ///     温度设定上限1
    /// </summary>
    public double TemperatureUpperLimit1 { get; set; }

    /// <summary>
    ///     温度设定上限2
    /// </summary>
    public double TemperatureUpperLimit2 { get; set; }

    /// <summary>
    ///     温度设定上限3
    /// </summary>
    public double TemperatureUpperLimit3 { get; set; }

    /// <summary>
    ///     温度设定下限1
    /// </summary>
    public double TemperatureLowerLimit1 { get; set; }

    /// <summary>
    ///     温度设定下限2
    /// </summary>
    public double TemperatureLowerLimit2 { get; set; }

    /// <summary>
    ///     温度设定下限3
    /// </summary>
    public double TemperatureLowerLimit3 { get; set; }

    #endregion 温度设定

    #region 实时温度

    /// <summary>
    /// 实际射砠温度
    /// </summary>
    public double ActualInjectionTemperature { get; set; }
    
    /// <summary>
    /// 温度1
    /// </summary>
    public double Temperature1 { get; set; }

    /// <summary>
    /// 温度2
    /// </summary>
    public double Temperature2 { get; set; }

    /// <summary>
    /// 温度3
    /// </summary>
    public double Temperature3 { get; set; }

    /// <summary>
    /// 温度4
    /// </summary>
    public double Temperature4 { get; set; }

    /// <summary>
    /// 良品数
    /// </summary>
    public int GoodCount { get; set; }

    /// <summary>
    ///     已开模数
    /// </summary>
    public int OpenedMoldCount { get; set; }

    #endregion 实时温度
    
    /// <summary>
    /// 模式 1：马达开；2：手动；3：半自动；4：全自动
    /// </summary>
    public ProCheSonModeEnum Mode { get; set; }
}

/// <summary>
/// 模式 1：马达开；2：手动；3：半自动；4：全自动
/// </summary>
public enum ProCheSonModeEnum
{

    /// <summary>
    /// 马达开
    /// </summary>
    MotorOn = 1,
    /// <summary>
    /// 手动
    /// </summary>
    ManualMode = 2,
    /// <summary>
    /// 半自动
    /// </summary>
    SemiAutoMode = 3,
    /// <summary>
    /// 全自动
    /// </summary>
    AutoMode = 4
}
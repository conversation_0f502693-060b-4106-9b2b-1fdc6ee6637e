using System.Globalization;
using System.IO.Ports;
using System.Linq;
using System.Net.Sockets;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using Common.Extension;
using Driver.Core.Models;
using Feng.Common.Extension;
using Furion.Logging;
using HslCommunication.BasicFramework;
using DateTime = System.DateTime;

namespace PORCHESON_Ps660Am;

/// <summary>
///     PS660AM系列解析规则
/// </summary>
public class ClientHandlerPs660Am : IDisposable
{
    private readonly PorchesonData _hxData;
    private readonly StringBuilder _receivedData = new();

    private readonly CancellationTokenSource _tokenSource = new();

    private int _port;
    private string _ip;

    /// <summary>
    ///     连接状态
    /// </summary>
    public bool IsConnected
    {
        get
        {
            try
            {
                if (_tcpClient == null && _serialPort == null)
                    return false;
                return _tcpClient == null || _tcpClient.Connected;
            }
            catch
            {
                return false;
            }
        }
    }

    public ClientHandlerPs660Am(PorchesonData hxData)
    {
        _hxData = hxData;
    }

    #region 串口

    private SerialPort _serialPort;

    public void Connect(string serialNumber)
    {
        _serialPort = new SerialPort();
        _serialPort.PortName = serialNumber;
        _serialPort.BaudRate = 57600;
        _serialPort.DataBits = 8;
        _serialPort.StopBits = StopBits.One;
        _serialPort.Parity = Parity.Even;
        _serialPort.Open();
        _ = _driverInfo.Socket.Send("Connected to serial port: " + _serialPort.PortName, _driverInfo.DeviceId + "_Logs");
        var receiveThread = new Thread(SerialReceiveData);
        receiveThread.Start();
    }

    private void SerialReceiveData()
    {
        var stream = _serialPort;
        while (!_tokenSource.IsCancellationRequested)
        {
            try
            {
                var buffer = new byte[10240];
                var bytesRead = stream.Read(buffer, 0, buffer.Length);
                if (bytesRead > 0)
                {
                    var resizedBuffer = new byte[bytesRead];
                    Array.Copy(buffer, resizedBuffer, resizedBuffer.Length);
                    buffer = resizedBuffer;
                    var data = SoftBasic.ByteToHexString(buffer, ' ');
                    _ = _driverInfo.Socket.Send($"来源:[{stream.PortName}]\t 时间：{DateTime.Now}\t【收】:" + data, _driverInfo.DeviceId + "_Logs");
                    if (_receivedData.Length > 0)
                        _receivedData.Append(' ');
                    _receivedData.Append(data);
                    ProcessMessages();
                }
            }
            catch (Exception ex)
            {
                if (!stream.IsOpen)
                {
                    _ = _driverInfo.Socket.Send("连接已断开", _driverInfo.DeviceId + "_Logs");
                    break;
                }
                _ = _driverInfo.Socket.Send("【收】 接收数据发生错误:" + ex.Message, _driverInfo.DeviceId + "_Logs");
            }

            Thread.Sleep(20);
        }
    }

    #endregion 串口

    /// <summary>
    /// </summary>
    private void ProcessMessages()
    {
        try
        {
            var startIndex = -1;
            var data = _receivedData.ToString();
            try
            {
                // 使用正则表达式匹配符合条件的报文头
                var match = Regex.Match(data, "(01 10|81 03|01 06 00 00)");
                if (!match.Success)
                {
                    _receivedData.Clear(); // 没有符合条件的报文头，清空接收缓冲区
                    return;
                }

                var foundValidHeader = false;
                // 遍历移除字符，直到找到符合条件的报文起始位置
                while (!foundValidHeader && !_tokenSource.IsCancellationRequested && data.IsNotNull())
                {
                    startIndex = data.IndexOf("01 10");
                    var startIndex2 = data.IndexOf("81 03");
                    var startIndex3 = data.IndexOf("01 06 00 00");

                    if (startIndex == -1 && startIndex2 == -1 && startIndex3 == -1)
                    {
                        _receivedData.Clear(); // 没有符合条件的报文，清空接收缓冲区
                        return;
                    }

                    if (startIndex2 != -1 && (startIndex == -1 || startIndex2 < startIndex))
                        startIndex = startIndex2;
                    else if (startIndex3 != -1 && (startIndex == -1 || startIndex3 < startIndex))
                        startIndex = startIndex3;

                    // 移除不符合条件的报文
                    _receivedData.Remove(0, startIndex);
                    // 移除后重新赋值对象
                    data = _receivedData.ToString();
                    // 检查移除后的数据是否以符合条件的报文起始
                    if (data.StartsWith("01 10") || data.StartsWith("81 03") || data.StartsWith("01 06 00 00"))
                        foundValidHeader = true;
                    else
                        _ = _driverInfo.Socket.Send("【收】 移除报文头异常字符", _driverInfo.DeviceId + "_Logs");
                }

                // 获取符合条件的报文头的索引位置
                startIndex = match.Index;
            }
            catch (Exception e)
            {
                Log.Error("【PORCHESON_Ps660Am】: 移除报文 error:" + e.Message);
            }

            var parts = data.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            var bytes = new byte[parts.Length];
            for (var i = 0; i < parts.Length; i++) bytes[i] = Convert.ToByte(parts[i], 16);

            while (startIndex != -1 && !_tokenSource.IsCancellationRequested)
                try
                {
                    if (bytes.Length - startIndex >= 8) // 至少包含帧头和长度字段
                    {
                        // 0x01 && 0x10 的帧头报文
                        if (bytes[0] == 0x01 && bytes[1] == 0x10)
                        {
                            // 解析命令字
                            var command = new byte[2];
                            Buffer.BlockCopy(bytes, 2, command, 0, command.Length);
                            // 解析长度
                            var length = new byte[2];
                            Buffer.BlockCopy(bytes, 4, length, 0, length.Length);

                            var lengthByte1 = length[0];
                            var lengthByte2 = length[1];
                            var packetLength = ((lengthByte1 << 8) | lengthByte2) * 2;

                            // 报文长度 + 帧头+功能吗+长度  + 帧尾 + 下标1
                            if (bytes.Length < packetLength + 6 + 2 + 1)
                            {
                                _ = _driverInfo.Socket.Send("【收】 检测到报文不完整,等待新的数据送达", _driverInfo.DeviceId + "_Logs");
                                break; // 报文不完整，等待下一次数据到达
                            }

                            // 解析报文体
                            var body = new byte[packetLength];
                            Buffer.BlockCopy(bytes, 6, body, 0, body.Length);

                            // 获取报文尾
                            var footer = new byte[2];
                            Buffer.BlockCopy(bytes, packetLength + 6 + 1, footer, 0, footer.Length);

                            Read0X01And0X10(command, body);
                            // 从接收缓冲区中移除已解析的报文
                            _receivedData.Remove(0, (packetLength + 6 + 2 + 1) * 3 - 1);
                            bytes = RemoveBytesFromArray(bytes, 0, packetLength + 6 + 2);
                        }
                        else if (bytes[0] == 0x81 && bytes[1] == 0x03)
                        {
                            // 解析长度
                            var length = new byte[1];
                            Buffer.BlockCopy(bytes, 2, length, 0, length.Length);

                            int packetLength = Convert.ToUInt16(length[0]);

                            // 报文长度 + 帧头+长度  + 帧尾
                            if (bytes.Length < packetLength + 3 + 2) break; // 报文不完整，等待下一次数据到达

                            // 解析报文体
                            var body = new byte[packetLength];
                            Buffer.BlockCopy(bytes, 3, body, 0, body.Length);

                            // 获取报文尾
                            var footer = new byte[2];
                            Buffer.BlockCopy(bytes, packetLength + 3, footer, 0, footer.Length);

                            Read0X81And0X03(length, body);
                            // 从接收缓冲区中移除已解析的报文
                            _receivedData.Remove(0, (packetLength + 3 + 2) * 3 - 1);
                            bytes = RemoveBytesFromArray(bytes, 0, packetLength + 3 + 2);
                        }
                        else if (bytes[0] == 0x01 && bytes[1] == 0x06)
                        {
                            // 解析命令字
                            var command = new byte[2];
                            Buffer.BlockCopy(bytes, 2, command, 0, command.Length);
                            var packetLength = 24;

                            // 报文长度 + 帧头+功能吗+长度  + 帧尾 + 下标1
                            if (bytes.Length < packetLength)
                            {
                                _ = _driverInfo.Socket.Send("【收】 检测到报文不完整,等待新的数据送达", _driverInfo.DeviceId + "_Logs");
                                break; // 报文不完整，等待下一次数据到达
                            }

                            // 解析报文体
                            var body = new byte[2];
                            Buffer.BlockCopy(bytes, 4, body, 0, body.Length);

                            ReadMode(command, body);
                            // 从接收缓冲区中移除已解析的报文
                            _receivedData.Remove(0, packetLength * 3 - 1);
                            bytes = RemoveBytesFromArray(bytes, 0, packetLength);
                        }
                        else
                        {
                            // 移除不符合条件报文
                            _receivedData.Remove(0, startIndex == 0 ? 1 : startIndex);
                            bytes = RemoveBytesFromArray(bytes, 0, startIndex == 0 ? 1 : startIndex / 2 - 1);
                        }
                        // 寻找下一个符合条件的报文头的索引位置
                        var match = Regex.Match(_receivedData.ToString(), "(01 10|81 03|01 06 00 00)");
                        if (match.Success)
                            startIndex = match.Index;
                        else
                        {
                            startIndex = -1;
                            _receivedData.Clear(); // 没有符合条件的报文，清空接收缓冲区               
                        }
                    }
                    else
                    {
                        _ = _driverInfo.Socket.Send("【收】 报文不完整,等待新的数据送达", _driverInfo.DeviceId + "_Logs");
                        break; // 报文不完整，等待下一次数据到达
                    }
                }
                catch (Exception e)
                {
                    startIndex = -1;
                    // 移除不符合条件报文
                    _receivedData.Remove(0, startIndex == 0 ? 1 : startIndex);
                    Log.Error($"【PORCHESON_Ps660Am】 {_ip}:{_port} value:【{SoftBasic.ByteToHexString(bytes, ' ')}】 报文解析 Error: " + e.Message);
                }
        }
        catch (Exception ex)
        {
            Log.Error($"【PORCHESON_Ps660Am】 {_ip}:{_port}  _receivedData:{_receivedData},解析方法 Error: " + ex.Message);
            _ = _driverInfo.Socket.Send("【PORCHESON_Ps660Am】 解析方法 Error: " + ex.Message, _driverInfo.DeviceId + "_Logs");
        }
    }

    private byte[] RemoveBytesFromArray(byte[] source, int startIndex, int length)
    {
        var destination = new byte[source.Length - length];
        Array.Copy(source, 0, destination, 0, startIndex);
        Array.Copy(source, startIndex + length, destination, startIndex, source.Length - startIndex - length);
        return destination;
    }

    /// <summary>
    ///     模式
    /// </summary>
    /// <param name="command"></param>
    /// <param name="body"></param>
    private void ReadMode(byte[] command, byte[] body)
    {
        // 模式
         if (command[0] == 0x00 && command[1] == 0x00)
        {
            if (body[0] == 0x00 && body[1] == 0x08)
                _hxData.Mode = ProCheSonModeEnum.MotorOn;
            else if (body[0] == 0x00 && body[1] == 0x00)
                _hxData.Mode = ProCheSonModeEnum.ManualMode;
            else if (body[0] == 0x00 && body[1] == 0x02)
                _hxData.Mode = ProCheSonModeEnum.SemiAutoMode;
            else if (body[0] == 0x00 && body[1] == 0x04)
                _hxData.Mode = ProCheSonModeEnum.AutoMode;
        }
    }

    /// <summary>
    ///     解析 0x01 0x10 报文
    /// </summary>
    /// <param name="command"></param>
    /// <param name="body"></param>
    private void Read0X01And0X10(byte[] command, byte[] body)
    {
        // 产量增加信号(spc)
        if (command[0] == 0x00 && command[1] == 0xDF)
            _hxData.OpenedMoldCount += 1;
        // 上电数据
        else if (command[0] == 0x00 && command[1] == 0x00)
            ReadPowerOnData(body);
        // 托模设定
        else if (command[0] == 0x01 && command[1] == 0x90)
            ReadMoldSetting(body);
        // 温度设定
        else if (command[0] == 0x07 && command[1] == 0x6c)
            ReadTemperatureSetting(body);
        // 射出设定
        else if (command[0] == 0x00 && command[1] == 0x64)
            ReadInjectionSetting(body);
        // 坐台设定
        else if (command[0] == 0x01 && command[1] == 0x2C)
            ReadTableSetting(body);
        // 储料、射退、冷却设定
        else if (command[0] == 0x00 && command[1] == 0xC8)
            ReadLubricationOrScrewColdSetting(body);
        // 计时计数设定
        else if (command[0] == 0x01 && command[1] == 0xF4)
            ReadTimingOrCount(body);
    }

    /// <summary>
    ///     上电数据
    /// </summary>
    private void ReadPowerOnData(byte[] body)
    {
        // 慢速合模压力
        _hxData.SlowCombinePressure = ParseHexValue(body, 21);
        // 快速合模压力
        _hxData.QuickCombinePressure = ParseHexValue(body, 23);
        // 低压合模压力
        _hxData.LowCombinePressure = ParseHexValue(body, 25);
        // 高压合模压力
        _hxData.HighCombinePressure = ParseHexValue(body, 27);
        // 慢速合模流量
        _hxData.SlowCombineFlow = ParseHexValue(body, 29);
        // 快速合模流量
        _hxData.QuickCombineFlow = ParseHexValue(body, 31);
        // 低压合模流量
        _hxData.LowCombineFlow = ParseHexValue(body, 33);
        // 高压合模流量
        _hxData.HighCombineFlow = ParseHexValue(body, 35);
        //37
        //39
        //41
        //43
        //45
        // 低压保护时间
        _hxData.LowPressureProtectionTime = ParseHexValue(body, 47, 0.01);
        // 开合模限时
        _hxData.MoldClosingTimeLimit = ParseHexValue(body, 49, 0.01);
        // 差动锁模
        _hxData.DifferentialModeLock = int.Parse(BitConverter.ToString(body.Skip(51).Take(2).ToArray()).Replace("-", ""), NumberStyles.HexNumber);
    }

    /// <summary>
    ///     托模设定
    /// </summary>
    private void ReadMoldSetting(byte[] body)
    {
        // 托模退压力
        _hxData.MoldRetreatPressure = ParseHexValue(body, 1);

        // 托进快压力
        _hxData.MoldAdvanceFastPressure = ParseHexValue(body, 3);
        // 托进慢压力
        _hxData.MoldAdvanceSlowPressure = ParseHexValue(body, 5);
        // 托模保持压力
        _hxData.MoldHoldPressure = ParseHexValue(body, 7);
        // 托模退流量
        _hxData.MoldRetreatFlow = ParseHexValue(body, 9);
        // 托进快流量
        _hxData.MoldAdvanceFastFlow = ParseHexValue(body, 11);
        // 托进慢流量
        _hxData.MoldAdvanceSlowFlow = ParseHexValue(body, 13);
        //15
        //17
        //19
        //..
        //59
        // 托模退位置
        _hxData.MoldRetreatPosition = ParseHexValue(body, 61);
        // 托进快位置
        _hxData.MoldAdvanceFastPosition = ParseHexValue(body, 63);
        // 托进慢位置    
        _hxData.MoldAdvanceSlowPosition = ParseHexValue(body, 65);
    }

    /// <summary>
    ///     射出设定
    /// </summary>
    private void ReadInjectionSetting(byte[] body)
    {
        // 射出压力5
        _hxData.InjectionPressure5 = ParseHexValue(body, 1);
        // 射出压力4
        _hxData.InjectionPressure4 = ParseHexValue(body, 3);
        // 射出压力3
        _hxData.InjectionPressure3 = ParseHexValue(body, 5);
        // 射出压力2
        _hxData.InjectionPressure2 = ParseHexValue(body, 7);
        // 射出压力1
        _hxData.InjectionPressure1 = ParseHexValue(body, 9);

        // 射出流量5
        _hxData.InjectionFlow5 = ParseHexValue(body, 11);
        // 射出流量4
        _hxData.InjectionFlow4 = ParseHexValue(body, 13);
        // 射出流量3
        _hxData.InjectionFlow3 = ParseHexValue(body, 15);
        // 射出流量2
        _hxData.InjectionFlow2 = ParseHexValue(body, 17);
        // 射出流量1
        _hxData.InjectionFlow1 = ParseHexValue(body, 19);

        //射出位置5
        _hxData.InjectionPosition5 = ParseHexValue(body, 21);
        //射出位置4
        _hxData.InjectionPosition4 = ParseHexValue(body, 23);
        //射出位置3
        _hxData.InjectionPosition3 = ParseHexValue(body, 25);
        //射出位置2
        _hxData.InjectionPosition2 = ParseHexValue(body, 27);
        //射出位置1
        _hxData.InjectionPosition1 = ParseHexValue(body, 29);
        //射出总时
        _hxData.InjectionTotalTime = ParseHexValue(body, 31, 0.01);
    }

    /// <summary>
    ///     坐台设定
    /// </summary>
    /// <param name="body"></param>
    private void ReadTableSetting(byte[] body)
    {
        // 座台进慢压力
        _hxData.TableAdvanceSlowPressure = ParseHexValue(body, 1);
        // 座台进快压力
        _hxData.TableAdvanceFastPressure = ParseHexValue(body, 3);
        // 座台退压力
        _hxData.TableRetreatPressure = ParseHexValue(body, 5);
        // 座台进慢流量
        _hxData.TableAdvanceSlowFlow = ParseHexValue(body, 7);
        // 座台进快流量
        _hxData.TableAdvanceFastFlow = ParseHexValue(body, 9);
        // 座台退流量
        _hxData.TableRetreatFlow = ParseHexValue(body, 11);
        // 座台进慢时间
        _hxData.TableAdvanceSlowTime = ParseHexValue(body, 13);
        // 座台进快时间
        _hxData.TableAdvanceFastTime = ParseHexValue(body, 15);
        // 座台退时间
        _hxData.TableRetreatTime = ParseHexValue(body, 17);
    }

    /// <summary>
    ///     储料、射退、冷却设定
    /// </summary>
    private void ReadLubricationOrScrewColdSetting(byte[] body)
    {
        // 储料前射退压力
        _hxData.PreInjectionRetreatPressure = ParseHexValue(body, 1);
        // 储料压力1
        _hxData.MaterialPressure1 = ParseHexValue(body, 3);
        // 储料压力2
        _hxData.MaterialPressure2 = ParseHexValue(body, 5);
        // 储料后射退压力
        _hxData.PostInjectionRetreatPressure = ParseHexValue(body, 7);
        // 储料前射退背压
        _hxData.PreInjectionRetreatBackPressure = ParseHexValue(body, 9);
        // 储料背压1
        _hxData.MaterialBackPressure1 = ParseHexValue(body, 11);

        // 储料背压2
        _hxData.MaterialBackPressure2 = ParseHexValue(body, 13);
        // 储料后射退背压
        _hxData.PostInjectionRetreatBackPressure = ParseHexValue(body, 15);
        // 储料前射退流量
        _hxData.PreInjectionRetreatFlow = ParseHexValue(body, 17);
        // 储料流量1
        _hxData.MaterialFlow1 = ParseHexValue(body, 19);
        // 储料流量2
        _hxData.MaterialFlow2 = ParseHexValue(body, 21);
        // 储料后射退流量
        _hxData.PostInjectionRetreatFlow = ParseHexValue(body, 23);
        // 储料前射退位置
        _hxData.PreInjectionRetreatPosition = ParseHexValue(body, 25);
        // 储料位置1
        _hxData.MaterialPosition1 = ParseHexValue(body, 27);
        // 储料位置2
        _hxData.MaterialPosition2 = ParseHexValue(body, 29);
        // 储料后射退位置
        _hxData.PostInjectionRetreatPosition = ParseHexValue(body, 31);
        // 冷却时间
        _hxData.CoolingTime = ParseHexValue(body, 33, 0.01);
        // 储料限时
        _hxData.MaterialLimitTime = ParseHexValue(body, 35, 0.01);
    }

    /// <summary>
    ///     计时计数设定
    /// </summary>
    /// <param name="body"></param>
    private void ReadTimingOrCount(byte[] body)
    {
        // 润滑模数
        _hxData.LubricationCycle = int.Parse(BitConverter.ToString(body.Skip(101).Take(2).ToArray()).Replace("-", ""), NumberStyles.HexNumber);
        // 循环等待时间
        _hxData.CycleWaitTime = ParseHexValue(body, 103, 0.01);
        // 润滑总时
        _hxData.TotalLubricationTime = ParseHexValue(body, 107, 0.01);
        // 手动动作限时
        _hxData.ManualActionTimeLimit = ParseHexValue(body, 109, 0.01);
        // 润滑时间
        _hxData.LubricationTime = ParseHexValue(body, 111, 0.01);
        // 故障告警时间
        _hxData.FaultWarningTime = ParseHexValue(body, 113, 0.01);
        // 润滑间歇
        _hxData.LubricationInterval = ParseHexValue(body, 115, 0.01);
        // 周期时间
        _hxData.CycleTime = ParseHexValue(body, 117, 0.01);
    }

    /// <summary>
    ///     解析 0x81 0x03 报文
    /// </summary>
    /// <param name="command"></param>
    /// <param name="body"></param>
    private void Read0X81And0X03(byte[] command, byte[] body)
    {
        //实时温度
        if (command[0] == 0xC8)
            // 实时温度
            RealTimeTemperature(body);
    }

    /// <summary>
    ///     实时温度
    /// </summary>
    /// <param name="body"></param>
    private void RealTimeTemperature(byte[] body)
    {
        // 实际射砠温度
        _hxData.ActualInjectionTemperature = ParseHexValue(body, 0);

        // 温度1
        _hxData.Temperature1 = ParseHexValue(body, 2);
        // 温度2
        _hxData.Temperature2 = ParseHexValue(body, 4);
        // 温度3
        _hxData.Temperature3 = ParseHexValue(body, 6);
        // 温度4
        _hxData.Temperature4 = ParseHexValue(body, 8);
        // 良品数
        _hxData.GoodCount = int.Parse(BitConverter.ToString(body.Skip(32).Take(2).ToArray()).Replace("-", ""), NumberStyles.HexNumber);
        // 已开模数
        _hxData.OpenedMoldCount = int.Parse(BitConverter.ToString(body.Skip(84).Take(2).ToArray()).Replace("-", ""), NumberStyles.HexNumber);
    }

    /// <summary>
    ///     温度设定
    /// </summary>
    private void ReadTemperatureSetting(byte[] body)
    {
        // 温度设定1
        _hxData.TemperatureSetting1 = ParseHexValue(body, 1);
        // 温度设定2
        _hxData.TemperatureSetting2 = ParseHexValue(body, 3);
        // 温度设定3
        _hxData.TemperatureSetting3 = ParseHexValue(body, 5);
        // 温度设定4
        _hxData.TemperatureSetting4 = ParseHexValue(body, 7);
        // 温度设定上限1
        _hxData.TemperatureUpperLimit1 = ParseHexValue(body, 41);
        // 温度设定上限2
        _hxData.TemperatureUpperLimit2 = ParseHexValue(body, 43);
        // 温度设定上限3
        _hxData.TemperatureUpperLimit3 = ParseHexValue(body, 45);
        // 温度设定下限1
        _hxData.TemperatureLowerLimit1 = ParseHexValue(body, 81);
        // 温度设定下限2
        _hxData.TemperatureLowerLimit2 = ParseHexValue(body, 83);
        // 温度设定下限3
        _hxData.TemperatureLowerLimit3 = ParseHexValue(body, 85);
    }

    /// <summary>
    /// </summary>
    /// <param name="data"></param>
    /// <param name="startIndex"></param>
    /// <param name="number"></param>
    /// <returns></returns>
    private double ParseHexValue(byte[] data, int startIndex, double number = 0.1)
    {
        //  27 0F 暂不处理
        var hexValue = BitConverter.ToString(data.Skip(startIndex).Take(2).ToArray()).Replace("-", "");
        return int.Parse(hexValue, NumberStyles.HexNumber) * number;
    }

    public void Dispose()
    {
        _tokenSource.Cancel();
        Thread.Sleep(200);
        _tcpClient?.Close();
        _tcpClient?.Dispose();
        _serialPort?.Close();
        _serialPort?.Dispose();
    }
}
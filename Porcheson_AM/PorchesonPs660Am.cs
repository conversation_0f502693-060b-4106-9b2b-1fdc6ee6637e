using System;
using System.IO;
using System.IO.Ports;
using System.Threading;
using HslCommunication;
using HslCommunication.LogNet;
using Newtonsoft.Json;

namespace PORCHESON_Ps660Am;

/// <summary>
/// PS660AM系列入口程序
/// </summary>
public class PorchesonPs660Am : IDisposable
{
    private readonly CancellationTokenSource _tokenSource = new();
    private readonly ILogNet logNet;
    private readonly PorchesonData _hxData = new();
    private readonly string deviceName = "/Edge/Porcheson_Ps660Am.txt";
    private ClientHandlerPs660Am _clientHandler;

    public PorchesonPs660Am(string portName)
    {
        try
        {
            logNet = new LogNetDateTime("./logs", GenerateMode.ByEveryDay, 7);
            logNet.WriteInfo($"Porcheson_Ps660Am 初始化--串口:{portName},波特率:{57600},数据位:{8},停止位:{1},校验位:{2}");

            // 创建客户端处理器
            _clientHandler = new ClientHandlerPs660Am(_hxData, logNet);

            // 连接串口
            _clientHandler.Connect(portName);

            if (File.Exists(deviceName))
            {
                var data = File.ReadAllText(deviceName);
                if (!string.IsNullOrEmpty(data))
                {
                    var hxData = JsonConvert.DeserializeObject<PorchesonData?>(data);
                    if (hxData != null)
                        _hxData = hxData;
                }
            }

            // 标记已经初始化完成，防止文件内容互相刷新
            init = true;

            logNet.WriteInfo($"Porcheson_Ps660Am 【数据】：{_hxData.ToJsonString()}");
            Thread.Sleep(1000 * 30);
            _tokenSource.Cancel();
        }
        catch (Exception ex)
        {
            logNet.WriteError("【Porcheson_Ps660Am】 初始化异常：" + ex.Message);
        }
        finally
        {
            logNet.WriteInfo($"Porcheson_Ps660Am 写入路径：{deviceName} ,值：{_hxData.ToJsonString()}");
            File.WriteAllText(deviceName, _hxData.ToJsonString());
            logNet.WriteInfo("Porcheson_Ps660Am 任务已经取消!");
        }
    }

    public void Dispose()
    {
        _clientHandler?.Dispose();
    }
}

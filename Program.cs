using System.Configuration;
using Reception.KeQiang;
using Reception.Porcheson;
using Reception.TechMation;

namespace Reception;

class Program
{
    static void Main()
    {
        ConfigurationManager.RefreshSection("appSettings");
        
        // 协议  Porcheson_Ps660Bm=宝捷信BM, TechMationAk=弘讯AK
        string? type ="";
        if (File.Exists("/Edge/type.txt"))
        {
            type = File.ReadAllText("/Edge/type.txt");
        }
        // 串口号
        string? portName = ConfigurationManager.AppSettings.Get("portName") ?? "/dev/ttyS4";

        if (type == "Porcheson_Ps660Bm")
        {
            PorchesonPs660Bm ps660Bm = new PorchesonPs660Bm(portName);
            ps660Bm.Dispose();
        }
        else if (type == "TechMationAk") 
        {
            TechMationAk techMationAk = new TechMationAk(portName);
            techMationAk.Dispose();
        }
        else if (type == "HongXunAkDouble") 
        {
            HongXunAkDouble hongXunAkDouble = new HongXunAkDouble(portName);
            hongXunAkDouble.Dispose();
        }
        else if (type == "KeQiangT6H3") 
        {
            KeQiangT6H3 keQiangT6H3 = new KeQiangT6H3(portName);
            keQiangT6H3.Dispose();
        }
        
        else
        {
            Console.WriteLine(string.IsNullOrEmpty(type) ? "未配置设备！" : $"暂不支持:{type}");
        }
    }
}
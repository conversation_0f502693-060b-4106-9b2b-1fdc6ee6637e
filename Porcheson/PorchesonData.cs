namespace Reception.Porcheson;

/// <summary>
///  宝捷信 注塑机字段
/// </summary>
public class PorchesonData
{
    #region 上电数据

    /// <summary>
    ///     快速合模压力
    /// </summary>
    public double QuickCombinePressure { get; set; }


    /// <summary>
    ///     快速合模流量
    /// </summary>
    public double QuickCombineFlow { get; set; }

    /// <summary>
    ///     低压合模压力
    /// </summary>
    public double LowCombinePressure { get; set; }

    /// <summary>
    ///     低压合模流量
    /// </summary>
    public double LowCombineFlow { get; set; }

    /// <summary>
    ///     高压合模压力
    /// </summary>
    public double HighCombinePressure { get; set; }

    /// <summary>
    ///     高压合模流量
    /// </summary>
    public double HighCombineFlow { get; set; }

    /// <summary>
    ///     高压合模时间
    /// </summary>
    public double HighCombineTime { get; set; }

    /// <summary>
    ///     低速开模压力
    /// </summary>
    public double LowSpeedOpenPressure { get; set; }

    /// <summary>
    ///     低速开模流量
    /// </summary>
    public double LowSpeedOpenFlow { get; set; }

    /// <summary>
    ///     卸荷时间
    /// </summary>
    public double UnloadTime { get; set; }

    /// <summary>
    ///     快速开模压力
    /// </summary>
    public double FastMoldPressure { get; set; }

    /// <summary>
    ///     快速开模流量
    /// </summary>
    public double FastMoldFlow { get; set; }

    /// <summary>
    ///     慢速开模时间
    /// </summary>
    public double SlowOpenTime { get; set; }

    /// <summary>
    ///     低速开模时间
    /// </summary>
    public double LowSpeedOpenTime { get; set; }

    /// <summary>
    ///     低压保护时间
    /// </summary>
    public double LowPressureProtectionTime { get; set; }

    /// <summary>
    ///     开合模限时
    /// </summary>
    public double MoldClosingTimeLimit { get; set; }

    /// <summary>
    ///     慢速开模时间
    /// </summary>
    public double SlowMoldOpeningTime { get; set; }

    /// <summary>
    ///     低速开模时间
    /// </summary>
    public double LowSpeedMoldOpeningTime { get; set; }

    /// <summary>
    ///     射出压力1
    /// </summary>
    public double InjectionPressure1 { get; set; }

    /// <summary>
    ///     射出流量1
    /// </summary>
    public double InjectionFlow1 { get; set; }

    /// <summary>
    ///     射出时间1
    /// </summary>
    public double InjectionTime1 { get; set; }

    /// <summary>
    ///     射出压力2
    /// </summary>
    public double InjectionPressure2 { get; set; }

    /// <summary>
    ///     射出流量2
    /// </summary>
    public double InjectionFlow2 { get; set; }

    /// <summary>
    ///     射出时间2
    /// </summary>
    public double InjectionTime2 { get; set; }

    /// <summary>
    ///     射出压力3
    /// </summary>
    public double InjectionPressure3 { get; set; }

    /// <summary>
    ///     射出流量3
    /// </summary>
    public double InjectionFlow3 { get; set; }

    /// <summary>
    ///     射出时间3
    /// </summary>
    public double InjectionTime3 { get; set; }

    /// <summary>
    ///     保压压力3
    /// </summary>
    public double HoldingPressure3 { get; set; }

    /// <summary>
    ///     保压流量3
    /// </summary>
    public double HoldingFlow3 { get; set; }

    /// <summary>
    ///     保压时间3
    /// </summary>
    public double HoldingTime3 { get; set; }

    /// <summary>
    ///     射出总时
    /// </summary>
    public double TotalInjectionTime { get; set; }

    /// <summary>
    ///     射出监测点
    /// </summary>
    public double InjectionMonitoringPoint { get; set; }

    /// <summary>
    ///     储料后射退压力
    /// </summary>
    public double PostShootingBackPressure { get; set; }

    /// <summary>
    ///     储料后射退流量
    /// </summary>
    public double PostShootingBackFlow { get; set; }

    /// <summary>
    ///     储料后射退时间
    /// </summary>
    public double PostShootingBackTime { get; set; }

    /// <summary>
    ///     储料压力
    /// </summary>
    public double MaterialStoragePressure { get; set; }

    /// <summary>
    ///     储料流量
    /// </summary>
    public double MaterialStorageFlow { get; set; }

    /// <summary>
    ///     储料前射退压力
    /// </summary>
    public double PreShootingBackPressure { get; set; }

    /// <summary>
    ///     储料前射退流量
    /// </summary>
    public double PreShootingBackFlow { get; set; }

    /// <summary>
    ///     储料前射退时间
    /// </summary>
    public double PreShootingBackTime { get; set; }

    /// <summary>
    ///     储料限时
    /// </summary>
    public double MaterialStorageTimeLimit { get; set; }

    /// <summary>
    ///     先冷却时间
    /// </summary>
    public double PreCoolingTime { get; set; }

    /// <summary>
    ///     后冷却时间
    /// </summary>
    public double PostCoolingTime { get; set; }

    /// <summary>
    ///     清料射退压力
    /// </summary>
    public double PurgeBackPressure { get; set; }

    /// <summary>
    ///     清料射退流量
    /// </summary>
    public double PurgeBackFlow { get; set; }

    /// <summary>
    ///     清料射退时间
    /// </summary>
    public double PurgeBackTime { get; set; }

    /// <summary>
    ///     清料压力
    /// </summary>
    public double PurgePressure { get; set; }

    /// <summary>
    ///     清料流量
    /// </summary>
    public double PurgeFlow { get; set; }

    /// <summary>
    ///     清料时间
    /// </summary>
    public double PurgeTime { get; set; }

    /// <summary>
    ///     清料射出压力
    /// </summary>
    public double PurgeInjectionPressure { get; set; }

    /// <summary>
    ///     清料射出流量
    /// </summary>
    public double PurgeInjectionFlow { get; set; }

    /// <summary>
    ///     清料射出时间
    /// </summary>
    public double PurgeInjectionTime { get; set; }

    /// <summary>
    ///     清料次数
    /// </summary>
    public int PurgeCount { get; set; }

    #endregion 上电数据

    #region 座台/托模设定

    /// <summary>
    ///     座台退压力
    /// </summary>
    public double TableRetreatPressure { get; set; }

    /// <summary>
    ///     座台退流量
    /// </summary>
    public double TableRetreatFlow { get; set; }

    /// <summary>
    ///     座台退时间
    /// </summary>
    public double TableRetreatTime { get; set; }

    /// <summary>
    ///     座台进压力
    /// </summary>
    public double TableAdvancePressure { get; set; }

    /// <summary>
    ///     座台进流量
    /// </summary>
    public double TableAdvanceFlow { get; set; }

    /// <summary>
    ///     座台进时间
    /// </summary>
    public double TableAdvanceTime { get; set; }

    /// <summary>
    ///     托模保持压力
    /// </summary>
    public double MoldHoldPressure { get; set; }

    /// <summary>
    ///     托模保持流量
    /// </summary>
    public double MoldHoldFlow { get; set; }

    /// <summary>
    ///     托模保持时间
    /// </summary>
    public double MoldHoldTime { get; set; }

    /// <summary>
    ///     托模进压力
    /// </summary>
    public double MoldAdvancePressure { get; set; }

    /// <summary>
    ///     托模进流量
    /// </summary>
    public double MoldAdvanceFlow { get; set; }

    /// <summary>
    ///     托模进时间
    /// </summary>
    public double MoldAdvanceTime { get; set; }

    /// <summary>
    ///     托模退压力
    /// </summary>
    public double MoldRetreatPressure { get; set; }

    /// <summary>
    ///     托模退流量
    /// </summary>
    public double MoldRetreatFlow { get; set; }

    /// <summary>
    ///     托模退时间
    /// </summary>
    public double MoldRetreatTime { get; set; }

    /// <summary>
    ///     托模次数
    /// </summary>
    public int MoldCount { get; set; }

    /// <summary>
    ///     托模进延迟时间
    /// </summary>
    public double MoldAdvanceDelayTime { get; set; }

    /// <summary>
    ///     托模退延迟时间
    /// </summary>
    public double MoldRetreatDelayTime { get; set; }

    #endregion 座台/托模设定

    #region 计时/计数设定，温度设定

    /// <summary>
    ///     润滑计时
    /// </summary>
    public double LubricationTiming { get; set; }

    /// <summary>
    ///     润滑模数
    /// </summary>
    public int LubricationCycle { get; set; }

    /// <summary>
    ///     循环等待时间
    /// </summary>
    public double CycleWaitTime { get; set; }

    /// <summary>
    ///     手动动作限时
    /// </summary>
    public double ManualActionTimeLimit { get; set; }

    /// <summary>
    ///     周期时间
    /// </summary>
    public double CycleTime { get; set; }

    /// <summary>
    ///     故障告警时间
    /// </summary>
    public double FaultWarningTime { get; set; }

    /// <summary>
    ///     射咀
    /// </summary>
    public int Nozzle { get; set; }

    /// <summary>
    ///     螺杆冷启动时间
    /// </summary>
    public double ScrewColdStartUpTime { get; set; }

    /// <summary>
    ///     设定模数
    /// </summary>
    public int SetMoldCount { get; set; }

    /// <summary>
    ///     已开模数
    /// </summary>
    public int OpenedMoldCount { get; set; }

    /// <summary>
    ///     卸荷压力
    /// </summary>
    public double UnloadPressure { get; set; }

    /// <summary>
    ///     卸荷流量
    /// </summary>
    public double UnloadFlow { get; set; }

    #endregion 计时/计数设定，温度设定

    #region 射出/保压设定

    /// <summary>
    ///     保压压力1
    /// </summary>
    public double HoldingPressure1 { get; set; }

    /// <summary>
    ///     保压流量1
    /// </summary>
    public double HoldingFlow1 { get; set; }

    /// <summary>
    ///     保压时间1
    /// </summary>
    public double HoldingTime1 { get; set; }

    /// <summary>
    ///     保压压力2
    /// </summary>
    public double HoldingPressure2 { get; set; }

    /// <summary>
    ///     保压流量2
    /// </summary>
    public double HoldingFlow2 { get; set; }

    /// <summary>
    ///     保压时间2
    /// </summary>
    public double HoldingTime2 { get; set; }

    /// <summary>
    ///     合模压力
    /// </summary>
    public double MoldClosingPressure { get; set; }

    /// <summary>
    ///     合模流量
    /// </summary>
    public double MoldClosingFlow { get; set; }

    /// <summary>
    ///     开模压力
    /// </summary>
    public double MoldOpeningPressure { get; set; }

    /// <summary>
    ///     开模流量
    /// </summary>
    public double MoldOpeningFlow { get; set; }

    #endregion 射出/保压设定

    #region 温度设定

    /// <summary>
    ///     温度设定1
    /// </summary>
    public double TemperatureSetting1 { get; set; }

    /// <summary>
    ///     温度设定2
    /// </summary>
    public double TemperatureSetting2 { get; set; }

    /// <summary>
    ///     温度设定3
    /// </summary>
    public double TemperatureSetting3 { get; set; }

    /// <summary>
    ///     温度设定4
    /// </summary>
    public double TemperatureSetting4 { get; set; }

    /// <summary>
    ///     温度设定5
    /// </summary>
    public double TemperatureSetting5 { get; set; }

    /// <summary>
    ///     温度设定6
    /// </summary>
    public double TemperatureSetting6 { get; set; }

    /// <summary>
    ///     温度设定7
    /// </summary>
    public double TemperatureSetting7 { get; set; }

    /// <summary>
    ///     温度设定8
    /// </summary>
    public double TemperatureSetting8 { get; set; }

    /// <summary>
    ///     温度设定9
    /// </summary>
    public double TemperatureSetting9 { get; set; }

    /// <summary>
    ///     温度设定10
    /// </summary>
    public double TemperatureSetting10 { get; set; }

    /// <summary>
    ///     温度设定上限1
    /// </summary>
    public double TemperatureUpperLimit1 { get; set; }

    /// <summary>
    ///     温度设定上限2
    /// </summary>
    public double TemperatureUpperLimit2 { get; set; }

    /// <summary>
    ///     温度设定上限3
    /// </summary>
    public double TemperatureUpperLimit3 { get; set; }

    /// <summary>
    ///     温度设定下限1
    /// </summary>
    public double TemperatureLowerLimit1 { get; set; }

    /// <summary>
    ///     温度设定下限2
    /// </summary>
    public double TemperatureLowerLimit2 { get; set; }

    /// <summary>
    ///     温度设定下限3
    /// </summary>
    public double TemperatureLowerLimit3 { get; set; }

    #endregion 温度设定

    /// <summary>
    /// 模式 1：马达开；2：手动；3：半自动；4：全自动
    /// </summary>
    public ProCheSonModeEnum Mode { get; set; }
}

/// <summary>
/// 模式 1：马达开；2：手动；3：半自动；4：全自动
/// </summary>
public enum ProCheSonModeEnum
{

    /// <summary>
    /// 马达开
    /// </summary>
    MotorOn = 1,
    /// <summary>
    /// 手动
    /// </summary>
    ManualMode = 2,
    /// <summary>
    /// 半自动
    /// </summary>
    SemiAutoMode = 3,
    /// <summary>
    /// 全自动
    /// </summary>
    AutoMode = 4
}
using System.Globalization;
using System.IO.Ports;
using System.Text;
using System.Text.RegularExpressions;
using HslCommunication;
using HslCommunication.BasicFramework;
using HslCommunication.LogNet;
using Newtonsoft.Json;

namespace Reception.Porcheson;

public class PorchesonPs660Bm : IDisposable
{
    private readonly SerialPort _serialPort;
    private readonly CancellationTokenSource _tokenSource = new();
    private readonly StringBuilder _receivedData = new();
    private readonly ILogNet logNet;
    private readonly bool init;
    private readonly PorchesonData _hxData = new();
    private readonly string deviceName = "/Edge/Porcheson_Ps660Bm.txt";

    public PorchesonPs660Bm(string portName)
    {
        try
        {
            logNet = new LogNetDateTime("./logs",GenerateMode.ByEveryDay,7);
            logNet.WriteInfo($"Porcheson_Ps660Bm 初始化--串口:{portName},波特率:{57600},数据位:{8},停止位:{1},校验位:{2}");

            _serialPort = new SerialPort();
            _serialPort.PortName = portName;
            _serialPort.BaudRate = 57600;
            _serialPort.DataBits = 8;
            _serialPort.StopBits = StopBits.One;
            _serialPort.Parity = Parity.Even;
            _serialPort.Open();

            logNet.WriteInfo("Porcheson_Ps660Bm 串口已开启 等待接受中······");
            var receiveThread = new Thread(SerialReceiveData);
            receiveThread.Start();

            if (File.Exists(deviceName))
            {
                var data = File.ReadAllText(deviceName);
                if (!string.IsNullOrEmpty(data))
                {
                    var hxData = JsonConvert.DeserializeObject<PorchesonData?>(data);
                    if (hxData != null)
                        _hxData = hxData;
                }
            }

            // 标记已经初始化完成，防止文件内容互相刷新
            init = true;

            // logNet.WriteInfo($"Porcheson_Ps660Bm 【数据】：{_hxData.ToJsonString()}");
            Thread.Sleep(1000 * 30);
            _tokenSource.Cancel();
        }
        catch (Exception ex)
        {
            logNet.WriteError("【Porcheson_Ps660Bm】 初始化异常：" + ex.Message);
        }
        finally
        {
            logNet.WriteInfo($"Porcheson_Ps660Bm 写入路径：{deviceName} ,值：{_hxData.ToJsonString()}");
            File.WriteAllText(deviceName, _hxData.ToJsonString());
            logNet.WriteInfo("Porcheson_Ps660Bm 任务已经取消!");
        }
    }

    public void Dispose()
    {
        _serialPort?.Close();
        _serialPort?.Dispose();
    }

    private void SerialReceiveData()
    {
        var stream = _serialPort;
        while (!_tokenSource.IsCancellationRequested)
        {
            try
            {
                var buffer = new byte[10240];
                var bytesRead = stream.Read(buffer, 0, buffer.Length);
                if (bytesRead > 0)
                {
                    var resizedBuffer = new byte[bytesRead];
                    Array.Copy(buffer, resizedBuffer, resizedBuffer.Length);
                    buffer = resizedBuffer;
                    var data = SoftBasic.ByteToHexString(buffer, ' ');

                    logNet.WriteInfo($"时间：{DateTime.Now}\t【收】:" + data);
                    if (_receivedData.Length > 0)
                        _receivedData.Append(' ');
                    _receivedData.Append(data);
                    // 初始化完成在开始解析
                    if (init)
                        ProcessMessages();
                }
            }
            catch (Exception ex)
            {
                logNet.WriteError("【收】 接收数据发生错误:" + ex.Message);
                break;
            }
        }
    }

    /// <summary>
    /// </summary>
    private void ProcessMessages()
    {
        try
        {
            var startIndex = -1;
            var data = _receivedData.ToString();
            try
            {
                // 使用正则表达式匹配符合条件的报文头
                var match = Regex.Match(data, "(01 10)");
                if (!match.Success)
                {
                    _receivedData.Clear(); // 没有符合条件的报文头，清空接收缓冲区
                    return;
                }

                var foundValidHeader = false;
                // 遍历移除字符，直到找到符合条件的报文起始位置
                while (!foundValidHeader && !_tokenSource.IsCancellationRequested)
                {
                    startIndex = data.IndexOf("01 10");

                    if (startIndex == -1)
                    {
                        _receivedData.Clear(); // 没有符合条件的报文，清空接收缓冲区
                        return;
                    }

                    // 移除不符合条件的报文
                    _receivedData.Remove(0, startIndex);
                    // 移除后重新赋值对象
                    data = _receivedData.ToString();
                    // 检查移除后的数据是否以符合条件的报文起始
                    if (data.StartsWith("01 10"))
                        foundValidHeader = true;
                    else
                        logNet.WriteInfo("【收】 移除报文头异常字符");
                }

                // 获取符合条件的报文头的索引位置
                startIndex = match.Index;
            }
            catch (Exception e)
            {
                logNet.WriteInfo("移除报文 error:" + e.Message);
            }

            var parts = data.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            var bytes = new byte[parts.Length];
            for (var i = 0; i < parts.Length; i++) bytes[i] = Convert.ToByte(parts[i], 16);

            while (startIndex != -1 && !_tokenSource.IsCancellationRequested)
                try
                {
                    if (bytes.Length - startIndex >= 8) // 至少包含帧头和长度字段
                    {
                        // 0x01 && 0x10 的帧头报文
                        if (bytes[0] == 0x01 && bytes[1] == 0x10)
                        {
                            // 解析命令字
                            var command = new byte[2];
                            Buffer.BlockCopy(bytes, 2, command, 0, command.Length);
                            // 解析长度
                            var length = new byte[2];
                            Buffer.BlockCopy(bytes, 4, length, 0, length.Length);

                            var lengthByte1 = length[0];
                            var lengthByte2 = length[1];
                            var packetLength = ((lengthByte1 << 8) | lengthByte2) * 2;

                            // 报文长度 + 帧头+功能吗+长度  + 帧尾 + 下标1
                            if (bytes.Length < packetLength + 6 + 2 + 1)
                            {
                                logNet.WriteInfo("【收】 检测到报文不完整,等待新的数据送达");
                                break; // 报文不完整，等待下一次数据到达
                            }

                            // 解析报文体
                            var body = new byte[packetLength];
                            Buffer.BlockCopy(bytes, 6, body, 0, body.Length);

                            // 获取报文尾
                            var footer = new byte[2];
                            Buffer.BlockCopy(bytes, packetLength + 6 + 1, footer, 0, footer.Length);

                            Read0X01And0X10(command, body);
                            // 从接收缓冲区中移除已解析的报文
                            _receivedData.Remove(0, (packetLength + 6 + 2 + 1) * 3 - 1);
                            bytes = RemoveBytesFromArray(bytes, 0, packetLength + 6 + 2 + 1);
                        }
                        else
                        {
                            // 移除不符合条件报文
                            _receivedData.Remove(0, startIndex == 0 ? 1 : startIndex);
                            bytes = RemoveBytesFromArray(bytes, 0, startIndex == 0 ? 1 : startIndex / 2 - 1);
                        }

                        // 寻找下一个符合条件的报文头的索引位置
                        var match = Regex.Match(_receivedData.ToString(), "(01 10)");
                        if (match.Success)
                        {
                            startIndex = match.Index;
                        }
                        else
                        {
                            startIndex = -1;
                            _receivedData.Clear(); // 没有符合条件的报文，清空接收缓冲区
                        }
                    }
                    else
                    {
                        logNet.WriteInfo("【收】 报文不完整,等待新的数据送达");
                        break; // 报文不完整，等待下一次数据到达
                    }
                }
                catch (Exception e)
                {
                    logNet.WriteInfo($"value:【{SoftBasic.ByteToHexString(bytes, ' ')}】 报文解析 Error: " + e.Message);
                    bytes = RemoveBytesFromArray(bytes, 0, bytes.Length);
                    // 移除不符合条件报文
                    _receivedData.Remove(0, startIndex == 0 ? 1 : startIndex);
                    startIndex = -1;
                }
        }
        catch (Exception ex)
        {
            logNet.WriteInfo($"_receivedData:{_receivedData} 解析方法 Error: " + ex.Message);
        }
    }

    private byte[] RemoveBytesFromArray(byte[] source, int startIndex, int length)
    {
        var destination = new byte[source.Length - length];
        Array.Copy(source, 0, destination, 0, startIndex);
        Array.Copy(source, startIndex + length, destination, startIndex, source.Length - startIndex - length);
        return destination;
    }

    /// <summary>
    ///     解析 0x01 0x10 报文
    /// </summary>
    /// <param name="command"></param>
    /// <param name="body"></param>
    private void Read0X01And0X10(byte[] command, byte[] body)
    {
        // // 产量增加信号(spc) 
        // if (command[0] == 0x00 && command[1] == 0xDF)
        //     _hxData.OpenedMoldCount += 1;
        // 上电数据
        if (command[0] == 0x00 && command[1] == 0x00)
            ReadPowerOnData(body);
        // 包括座台/托模设定
        else if (command[0] == 0x00 && command[1] == 0x64)
            ReadMoldOrTableSetting(body);
        // 包括计时/计数设定，温度设定
        else if (command[0] == 0x00 && command[1] == 0xC8)
            ReadTimeOrCountSetting(body);
        // 包括射出/保压设定
        else if (command[0] == 0x03 && command[1] == 0x84)
            ReadLubricationOrScrewColdSetting(body);
        // 包括温度设定
        else if (command[0] == 0x07 && command[1] == 0x6c)
            ReadTemperatureSetting(body);
    }

    /// <summary>
    ///     上电数据
    /// </summary>
    private void ReadPowerOnData(byte[] body)
    {
        // 快速合模压力
        _hxData.QuickCombinePressure = ParseHexValue(body, 21);
        // 快速合模流量
        _hxData.QuickCombineFlow = ParseHexValue(body, 23);
        //25
        // 低压合模压力
        _hxData.LowCombinePressure = ParseHexValue(body, 27);
        // 低压合模流量
        _hxData.LowCombineFlow = ParseHexValue(body, 29);
        // 31
        // 高压合模压力
        _hxData.HighCombinePressure = ParseHexValue(body, 33);
        // 高压合模流量
        _hxData.HighCombineFlow = ParseHexValue(body, 35);
        // 高压合模时间
        _hxData.HighCombineTime = ParseHexValue(body, 37, 0.01);
        // 低速开模压力
        _hxData.LowSpeedOpenPressure = ParseHexValue(body, 39);
        // 低速开模流量
        _hxData.LowSpeedOpenFlow = ParseHexValue(body, 41);
        // 43
        // 卸荷时间
        _hxData.UnloadTime = ParseHexValue(body, 45, 0.01);
        // 快速开模压力
        _hxData.FastMoldPressure = ParseHexValue(body, 47);
        // 快速开模流量
        _hxData.FastMoldFlow = ParseHexValue(body, 49);
        // 51
        // 慢速开模压力
        _hxData.SlowOpenTime = ParseHexValue(body, 53);
        // 慢速开模流量
        _hxData.LowSpeedOpenTime = ParseHexValue(body, 55);
        //57
        // 低压保护时间
        _hxData.LowPressureProtectionTime = ParseHexValue(body, 59, 0.01);
        // 开合模限时
        _hxData.MoldClosingTimeLimit = ParseHexValue(body, 61);
        //63
        //65
        //67
        //69
        // 慢速开模时间
        _hxData.SlowMoldOpeningTime = ParseHexValue(body, 71, 0.01);
        // 低速开模时间
        _hxData.LowSpeedMoldOpeningTime = ParseHexValue(body, 73, 0.01);
        //75
        //77
        //79
        // 射出压力1
        _hxData.InjectionPressure1 = ParseHexValue(body, 81);
        // 射出流量1
        _hxData.InjectionFlow1 = ParseHexValue(body, 83);
        // 射出时间1
        _hxData.InjectionTime1 = ParseHexValue(body, 85, 0.01);
        // 射出压力2
        _hxData.InjectionPressure2 = ParseHexValue(body, 87);
        // 射出流量2
        _hxData.InjectionFlow2 = ParseHexValue(body, 89);
        // 射出时间2
        _hxData.InjectionTime2 = ParseHexValue(body, 91, 0.01);
        // 射出压力3
        _hxData.InjectionPressure3 = ParseHexValue(body, 93);
        // 射出流量3
        _hxData.InjectionFlow3 = ParseHexValue(body, 95);
        // 射出时间3
        _hxData.InjectionTime3 = ParseHexValue(body, 97, 0.01);
        // 保压压力3
        _hxData.HoldingPressure3 = ParseHexValue(body, 99);
        // 保压流量3
        _hxData.HoldingFlow3 = ParseHexValue(body, 101);
        // 保压时间3
        _hxData.HoldingTime3 = ParseHexValue(body, 103, 0.01);
        // 射出总时
        _hxData.TotalInjectionTime = ParseHexValue(body, 105);
        //107
        //109
        //111
        // 射出监测点
        _hxData.InjectionMonitoringPoint = ParseHexValue(body, 113);
        //115
        //117
        //119
        // 储料后射退压力
        _hxData.PostShootingBackPressure = ParseHexValue(body, 121);
        // 储料后射退流量
        _hxData.PostShootingBackFlow = ParseHexValue(body, 123);
        //储料后射退时间
        _hxData.PostShootingBackTime = ParseHexValue(body, 125, 0.01);
        //储料压力
        _hxData.MaterialStoragePressure = ParseHexValue(body, 127);
        //储料流量
        _hxData.MaterialStorageFlow = ParseHexValue(body, 129);
        //131
        //储料前射退压力
        _hxData.PreShootingBackPressure = ParseHexValue(body, 133);
        //储料前射退流量
        _hxData.PreShootingBackFlow = ParseHexValue(body, 135);
        //储料前射退时间
        _hxData.PreShootingBackTime = ParseHexValue(body, 137, 0.01);
        //储料限时
        _hxData.MaterialStorageTimeLimit = ParseHexValue(body, 139);
        //先冷却时间
        _hxData.PreCoolingTime = ParseHexValue(body, 141, 0.01);
        //后冷却时间
        _hxData.PostCoolingTime = ParseHexValue(body, 143, 0.01);
        // 145
        // 147
        // 149
        // 151
        // 153
        // 155
        // 157
        // 159
        //清料射退压力
        _hxData.PurgeBackPressure = ParseHexValue(body, 161);
        //清料射退流量
        _hxData.PurgeBackFlow = ParseHexValue(body, 163);
        //清料射退时间
        _hxData.PurgeBackTime = ParseHexValue(body, 165, 0.01);
        //清料压力
        _hxData.PurgePressure = ParseHexValue(body, 167);
        //清料流量
        _hxData.PurgeFlow = ParseHexValue(body, 169);
        //清料时间
        _hxData.PurgeTime = ParseHexValue(body, 171, 0.01);
        //清料射出压力
        _hxData.PurgeInjectionPressure = ParseHexValue(body, 173);
        //清料射出流量
        _hxData.PurgeInjectionFlow = ParseHexValue(body, 175);
        //清料射出时间
        _hxData.PurgeInjectionTime = ParseHexValue(body, 177, 0.01);
        //清料次数
        _hxData.PurgeCount = int.Parse(BitConverter.ToString(body.Skip(179).Take(2).ToArray()).Replace("-", ""), NumberStyles.HexNumber);
    }

    /// <summary>
    ///     座台/托模设定
    /// </summary>
    private void ReadMoldOrTableSetting(byte[] body)
    {
        // 座台退压力
        _hxData.TableRetreatPressure = ParseHexValue(body, 1);
        // 座台退流量
        _hxData.TableRetreatFlow = ParseHexValue(body, 3);
        // 座台退时间
        _hxData.TableRetreatTime = ParseHexValue(body, 5, 0.01);
        //7
        // 座台进压力
        _hxData.TableAdvancePressure = ParseHexValue(body, 9);
        // 座台进流量
        _hxData.TableAdvanceFlow = ParseHexValue(body, 11);
        // 座台进时间
        _hxData.TableAdvanceTime = ParseHexValue(body, 13, 0.01);
        // 托模保持压力
        _hxData.MoldHoldPressure = ParseHexValue(body, 15);
        // 托模保持流量
        _hxData.MoldHoldFlow = ParseHexValue(body, 17);
        // 托模保持时间
        _hxData.MoldHoldTime = ParseHexValue(body, 19, 0.01);
        //21
        // 托模进压力
        _hxData.MoldAdvancePressure = ParseHexValue(body, 23);
        // 托模进流量
        _hxData.MoldAdvanceFlow = ParseHexValue(body, 25);
        // 托模进时间
        _hxData.MoldAdvanceTime = ParseHexValue(body, 29, 0.01);
        // 托模退压力
        _hxData.MoldRetreatPressure = ParseHexValue(body, 31);
        // 托模退流量
        _hxData.MoldRetreatFlow = ParseHexValue(body, 33);
        // 托模退时间
        _hxData.MoldRetreatTime = ParseHexValue(body, 35, 0.01);
        // 托模次数
        _hxData.MoldCount = int.Parse(BitConverter.ToString(body.Skip(37).Take(2).ToArray()).Replace("-", ""), NumberStyles.HexNumber);
        // 托模进延迟时间
        _hxData.MoldAdvanceDelayTime = ParseHexValue(body, 39, 0.01);
        //托模退延迟时间
        _hxData.MoldRetreatDelayTime = ParseHexValue(body, 41, 0.01);
    }

    /// <summary>
    ///     计时/计数设定，温度设定
    /// </summary>
    private void ReadTimeOrCountSetting(byte[] body)
    {
        // 润滑计时
        _hxData.LubricationTiming = ParseHexValue(body, 1, 0.01);
        // 润滑模数
        _hxData.LubricationCycle = int.Parse(BitConverter.ToString(body.Skip(3).Take(2).ToArray()).Replace("-", ""), NumberStyles.HexNumber);
        // 循环等待时间
        _hxData.CycleWaitTime = ParseHexValue(body, 5, 0.01);
        // 手动动作限时
        _hxData.ManualActionTimeLimit = ParseHexValue(body, 7, 0.01);
        // 周期时间
        _hxData.CycleTime = ParseHexValue(body, 9, 0.01);
        // 故障告警时间
        _hxData.FaultWarningTime = ParseHexValue(body, 11, 0.01);
        //13
        //15
        // 射咀
        _hxData.Nozzle = int.Parse(BitConverter.ToString(body.Skip(17).Take(2).ToArray()).Replace("-", ""), NumberStyles.HexNumber);
        // 螺杆冷启动时间
        _hxData.ScrewColdStartUpTime = ParseHexValue(body, 19, 0.01);
        //21
        //23
        //25
        //27
        //29
        //31
        //33
        //35
        //37
        //39
        // 设定模数
        _hxData.SetMoldCount = int.Parse(BitConverter.ToString(body.Skip(41).Take(2).ToArray()).Replace("-", ""), NumberStyles.HexNumber);
        //43
        //45
        // 已开模数
        _hxData.OpenedMoldCount = int.Parse(BitConverter.ToString(body.Skip(47).Take(2).ToArray()).Replace("-", ""), NumberStyles.HexNumber);
        // 卸荷压力
        _hxData.UnloadPressure = ParseHexValue(body, 121);
        // 卸荷流量
        _hxData.UnloadFlow = ParseHexValue(body, 123);
    }

    /// <summary>
    ///     射出/保压设定
    /// </summary>
    private void ReadLubricationOrScrewColdSetting(byte[] body)
    {
        // 保压压力1
        _hxData.HoldingPressure1 = ParseHexValue(body, 21);
        // 保压流量1
        _hxData.HoldingFlow1 = ParseHexValue(body, 23);
        // 保压时间1
        _hxData.HoldingTime1 = ParseHexValue(body, 25, 0.01);
        // 保压压力2
        _hxData.HoldingPressure2 = ParseHexValue(body, 27);
        // 保压流量2
        _hxData.HoldingFlow2 = ParseHexValue(body, 29);
        // 保压时间2
        _hxData.HoldingTime2 = ParseHexValue(body, 31, 0.01);
        //33
        //35
        //37
        //39
        //41
        //43
        //45
        //47
        //49
        //51
        // 合模压力
        _hxData.MoldClosingPressure = ParseHexValue(body, 53);
        // 合模流量
        _hxData.MoldClosingFlow = ParseHexValue(body, 55);
        // 开模压力
        _hxData.MoldOpeningPressure = ParseHexValue(body, 57);
        // 开模流量
        _hxData.MoldOpeningFlow = ParseHexValue(body, 59);
    }

    /// <summary>
    ///     温度设定
    /// </summary>
    private void ReadTemperatureSetting(byte[] body)
    {
        // 温度设定1
        _hxData.TemperatureSetting1 = ParseHexValue(body, 1);
        // 温度设定2
        _hxData.TemperatureSetting2 = ParseHexValue(body, 3);
        // 温度设定3
        _hxData.TemperatureSetting3 = ParseHexValue(body, 5);
        // 温度设定4
        _hxData.TemperatureSetting4 = ParseHexValue(body, 7);
        if (body.Length <= 9) return;
        // 温度设定5
        _hxData.TemperatureSetting5 = ParseHexValue(body, 9);
        // 温度设定6
        _hxData.TemperatureSetting6 = ParseHexValue(body, 11);
        // 温度设定7
        _hxData.TemperatureSetting7 = ParseHexValue(body, 13);
        // 温度设定8
        _hxData.TemperatureSetting8 = ParseHexValue(body, 15);
        // 温度设定9
        _hxData.TemperatureSetting9 = ParseHexValue(body, 17);
        // 温度设定10
        _hxData.TemperatureSetting10 = ParseHexValue(body, 19);
        // 温度设定上限1
        _hxData.TemperatureUpperLimit1 = ParseHexValue(body, 41);
        // 温度设定上限2
        _hxData.TemperatureUpperLimit2 = ParseHexValue(body, 43);
        // 温度设定上限3
        _hxData.TemperatureUpperLimit3 = ParseHexValue(body, 45);
        // 温度设定下限1
        _hxData.TemperatureLowerLimit1 = ParseHexValue(body, 81);
        // 温度设定下限2
        _hxData.TemperatureLowerLimit2 = ParseHexValue(body, 83);
        // 温度设定下限3
        _hxData.TemperatureLowerLimit3 = ParseHexValue(body, 85);
    }

    /// <summary>
    /// </summary>
    /// <param name="data"></param>
    /// <param name="startIndex"></param>
    /// <param name="number"></param>
    /// <returns></returns>
    private double ParseHexValue(byte[] data, int startIndex, double number = 0.1)
    {
        //  27 0F 暂不处理
        var hexValue = BitConverter.ToString(data.Skip(startIndex).Take(2).ToArray()).Replace("-", "");
        return int.Parse(hexValue, NumberStyles.HexNumber) * number;
    }
}